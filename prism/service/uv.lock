version = 1
revision = 2
requires-python = ">=3.11"
resolution-markers = [
    "python_full_version >= '3.13'",
    "python_full_version == '3.12.*'",
    "python_full_version < '3.12'",
]

[[package]]
name = "aioboto3"
version = "14.1.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "aiobotocore", extra = ["boto3"] },
    { name = "aiofiles" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aioboto3/14.1.0/aioboto3-14.1.0.tar.gz", hash = "sha256:9d59b536ae8a951b9413ce151bf77df9c7cfe2cbaa2c4c240c066f384305c932" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aioboto3/14.1.0/aioboto3-14.1.0-py3-none-any.whl", hash = "sha256:f8547032bc4f90966b22869c1295d890c161549f4e8919f32853571ceb6fd0c6" },
]

[[package]]
name = "aiobotocore"
version = "2.21.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "aiohttp" },
    { name = "aioitertools" },
    { name = "botocore" },
    { name = "jmespath" },
    { name = "multidict" },
    { name = "python-dateutil" },
    { name = "wrapt" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiobotocore/2.21.1/aiobotocore-2.21.1.tar.gz", hash = "sha256:010357f43004413e92a9d066bb0db1f241aeb29ffed306e9197061ffc94e6577" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiobotocore/2.21.1/aiobotocore-2.21.1-py3-none-any.whl", hash = "sha256:bd7c49a6d6f8a3d9444b0a94417c8da13813b5c7eec1c4f0ec2db7e8ce8f23e7" },
]

[package.optional-dependencies]
boto3 = [
    { name = "boto3" },
]

[[package]]
name = "aiofiles"
version = "24.1.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiofiles/24.1.0/aiofiles-24.1.0.tar.gz", hash = "sha256:22a075c9e5a3810f0c2e48f3008c94d68c65d763b9b03857924c99e57355166c" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiofiles/24.1.0/aiofiles-24.1.0-py3-none-any.whl", hash = "sha256:b4ec55f4195e3eb5d7abd1bf7e061763e864dd4954231fb8539a0ef8bb8260e5" },
]

[[package]]
name = "aiohappyeyeballs"
version = "2.6.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohappyeyeballs/2.6.1/aiohappyeyeballs-2.6.1.tar.gz", hash = "sha256:c3f9d0113123803ccadfdf3f0faa505bc78e6a72d1cc4806cbd719826e943558" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohappyeyeballs/2.6.1/aiohappyeyeballs-2.6.1-py3-none-any.whl", hash = "sha256:f349ba8f4b75cb25c99c5c2d84e997e485204d2902a9597802b0371f09331fb8" },
]

[[package]]
name = "aiohttp"
version = "3.11.14"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "aiohappyeyeballs" },
    { name = "aiosignal" },
    { name = "attrs" },
    { name = "frozenlist" },
    { name = "multidict" },
    { name = "propcache" },
    { name = "yarl" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14.tar.gz", hash = "sha256:d6edc538c7480fa0a3b2bdd705f8010062d74700198da55d16498e1b49549b9c" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-macosx_10_9_universal2.whl", hash = "sha256:f296d637a50bb15fb6a229fbb0eb053080e703b53dbfe55b1e4bb1c5ed25d325" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:ec6cd1954ca2bbf0970f531a628da1b1338f594bf5da7e361e19ba163ecc4f3b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:572def4aad0a4775af66d5a2b5923c7de0820ecaeeb7987dcbccda2a735a993f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1c68e41c4d576cd6aa6c6d2eddfb32b2acfb07ebfbb4f9da991da26633a3db1a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:99b8bbfc8111826aa8363442c0fc1f5751456b008737ff053570f06a151650b3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:4b0a200e85da5c966277a402736a96457b882360aa15416bf104ca81e6f5807b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:d173c0ac508a2175f7c9a115a50db5fd3e35190d96fdd1a17f9cb10a6ab09aa1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:413fe39fd929329f697f41ad67936f379cba06fcd4c462b62e5b0f8061ee4a77" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:65c75b14ee74e8eeff2886321e76188cbe938d18c85cff349d948430179ad02c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-musllinux_1_2_armv7l.whl", hash = "sha256:321238a42ed463848f06e291c4bbfb3d15ba5a79221a82c502da3e23d7525d06" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:59a05cdc636431f7ce843c7c2f04772437dd816a5289f16440b19441be6511f1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-musllinux_1_2_ppc64le.whl", hash = "sha256:daf20d9c3b12ae0fdf15ed92235e190f8284945563c4b8ad95b2d7a31f331cd3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-musllinux_1_2_s390x.whl", hash = "sha256:05582cb2d156ac7506e68b5eac83179faedad74522ed88f88e5861b78740dc0e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:12c5869e7ddf6b4b1f2109702b3cd7515667b437da90a5a4a50ba1354fe41881" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-win32.whl", hash = "sha256:92868f6512714efd4a6d6cb2bfc4903b997b36b97baea85f744229f18d12755e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp311-cp311-win_amd64.whl", hash = "sha256:bccd2cb7aa5a3bfada72681bdb91637094d81639e116eac368f8b3874620a654" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:70ab0f61c1a73d3e0342cedd9a7321425c27a7067bebeeacd509f96695b875fc" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:602d4db80daf4497de93cb1ce00b8fc79969c0a7cf5b67bec96fa939268d806a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:3a8a0d127c10b8d89e69bbd3430da0f73946d839e65fec00ae48ca7916a31948" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:ca9f835cdfedcb3f5947304e85b8ca3ace31eef6346d8027a97f4de5fb687534" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:8aa5c68e1e68fff7cd3142288101deb4316b51f03d50c92de6ea5ce646e6c71f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:3b512f1de1c688f88dbe1b8bb1283f7fbeb7a2b2b26e743bb2193cbadfa6f307" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:cc9253069158d57e27d47a8453d8a2c5a370dc461374111b5184cf2f147a3cc3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:0b2501f1b981e70932b4a552fc9b3c942991c7ae429ea117e8fba57718cdeed0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:28a3d083819741592685762d51d789e6155411277050d08066537c5edc4066e6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-musllinux_1_2_armv7l.whl", hash = "sha256:0df3788187559c262922846087e36228b75987f3ae31dd0a1e5ee1034090d42f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:9e73fa341d8b308bb799cf0ab6f55fc0461d27a9fa3e4582755a3d81a6af8c09" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:51ba80d473eb780a329d73ac8afa44aa71dfb521693ccea1dea8b9b5c4df45ce" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:8d1dd75aa4d855c7debaf1ef830ff2dfcc33f893c7db0af2423ee761ebffd22b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:41cf0cefd9e7b5c646c2ef529c8335e7eafd326f444cc1cdb0c47b6bc836f9be" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-win32.whl", hash = "sha256:948abc8952aff63de7b2c83bfe3f211c727da3a33c3a5866a0e2cf1ee1aa950f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp312-cp312-win_amd64.whl", hash = "sha256:3b420d076a46f41ea48e5fcccb996f517af0d406267e31e6716f480a3d50d65c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:8d14e274828561db91e4178f0057a915f3af1757b94c2ca283cb34cbb6e00b50" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:f30fc72daf85486cdcdfc3f5e0aea9255493ef499e31582b34abadbfaafb0965" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:4edcbe34e6dba0136e4cabf7568f5a434d89cc9de5d5155371acda275353d228" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1a7169ded15505f55a87f8f0812c94c9412623c744227b9e51083a72a48b68a5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:ad1f2fb9fe9b585ea4b436d6e998e71b50d2b087b694ab277b30e060c434e5db" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:20412c7cc3720e47a47e63c0005f78c0c2370020f9f4770d7fc0075f397a9fb0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:6dd9766da617855f7e85f27d2bf9a565ace04ba7c387323cd3e651ac4329db91" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:599b66582f7276ebefbaa38adf37585e636b6a7a73382eb412f7bc0fc55fb73d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:b41693b7388324b80f9acfabd479bd1c84f0bc7e8f17bab4ecd9675e9ff9c734" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-musllinux_1_2_armv7l.whl", hash = "sha256:86135c32d06927339c8c5e64f96e4eee8825d928374b9b71a3c42379d7437058" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:04eb541ce1e03edc1e3be1917a0f45ac703e913c21a940111df73a2c2db11d73" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:dc311634f6f28661a76cbc1c28ecf3b3a70a8edd67b69288ab7ca91058eb5a33" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:69bb252bfdca385ccabfd55f4cd740d421dd8c8ad438ded9637d81c228d0da49" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:2b86efe23684b58a88e530c4ab5b20145f102916bbb2d82942cafec7bd36a647" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-win32.whl", hash = "sha256:b9c60d1de973ca94af02053d9b5111c4fbf97158e139b14f1be68337be267be6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiohttp/3.11.14/aiohttp-3.11.14-cp313-cp313-win_amd64.whl", hash = "sha256:0a29be28e60e5610d2437b5b2fed61d6f3dcde898b57fb048aa5079271e7f6f3" },
]

[[package]]
name = "aioitertools"
version = "0.12.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aioitertools/0.12.0/aioitertools-0.12.0.tar.gz", hash = "sha256:c2a9055b4fbb7705f561b9d86053e8af5d10cc845d22c32008c43490b2d8dd6b" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aioitertools/0.12.0/aioitertools-0.12.0-py3-none-any.whl", hash = "sha256:fc1f5fac3d737354de8831cbba3eb04f79dd649d8f3afb4c5b114925e662a796" },
]

[[package]]
name = "aiokafka"
version = "0.12.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "async-timeout" },
    { name = "packaging" },
    { name = "typing-extensions" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0.tar.gz", hash = "sha256:62423895b866f95b5ed8d88335295a37cc5403af64cb7cb0e234f88adc2dff94" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:ddc5308c43d48af883667e2f950a0a9739ce2c9bfe69a0b55dc234f58b1b42d6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:ff63689cafcd6dd642a15de75b7ae121071d6162cccba16d091bcb28b3886307" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:24633931e05a9dc80555a2f845572b6845d2dcb1af12de27837b8602b1b8bc74" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:42b2436c7c69384d210e9169fbfe339d9f49dbdcfddd8d51c79b9877de545e33" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp311-cp311-win32.whl", hash = "sha256:90511a2c4cf5f343fc2190575041fbc70171654ab0dae64b3bbabd012613bfa7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp311-cp311-win_amd64.whl", hash = "sha256:04c8ad27d04d6c53a1859687015a5f4e58b1eb221e8a7342d6c6b04430def53e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:b01947553ff1120fa1cb1a05f2c3e5aa47a5378c720bafd09e6630ba18af02aa" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:e3c8ec1c0606fa645462c7353dc3e4119cade20c4656efa2031682ffaad361c0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:577c1c48b240e9eba57b3d2d806fb3d023a575334fc3953f063179170cc8964f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:d7b815b2e5fed9912f1231be6196547a367b9eb3380b487ff5942f0c73a3fb5c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp312-cp312-win32.whl", hash = "sha256:5a907abcdf02430df0829ac80f25b8bb849630300fa01365c76e0ae49306f512" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp312-cp312-win_amd64.whl", hash = "sha256:fdbd69ec70eea4a8dfaa5c35ff4852e90e1277fcc426b9380f0b499b77f13b16" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:f9e8ab97b935ca681a5f28cf22cf2b5112be86728876b3ec07e4ed5fc6c21f2d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:ed991c120fe19fd9439f564201dd746c4839700ef270dd4c3ee6d4895f64fe83" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:2c01abf9787b1c3f3af779ad8e76d5b74903f590593bc26f33ed48750503e7f7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:08c84b3894d97fd02fcc8886f394000d0f5ce771fab5c498ea2b0dd2f6b46d5b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp313-cp313-win32.whl", hash = "sha256:63875fed922c8c7cf470d9b2a82e1b76b4a1baf2ae62e07486cf516fd09ff8f2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiokafka/0.12.0/aiokafka-0.12.0-cp313-cp313-win_amd64.whl", hash = "sha256:bdc0a83eb386d2384325d6571f8ef65b4cfa205f8d1c16d7863e8d10cacd995a" },
]

[[package]]
name = "aiosignal"
version = "1.3.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "frozenlist" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiosignal/1.3.2/aiosignal-1.3.2.tar.gz", hash = "sha256:a8c255c66fafb1e499c9351d0bf32ff2d8a0321595ebac3b93713656d2436f54" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/aiosignal/1.3.2/aiosignal-1.3.2-py2.py3-none-any.whl", hash = "sha256:45cde58e409a301715980c2b01d0c28bdde3770d8290b5eb2173759d9acb31a5" },
]

[[package]]
name = "argon2-cffi"
version = "23.1.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "argon2-cffi-bindings" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/argon2-cffi/23.1.0/argon2_cffi-23.1.0.tar.gz", hash = "sha256:879c3e79a2729ce768ebb7d36d4609e3a78a4ca2ec3a9f12286ca057e3d0db08" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/argon2-cffi/23.1.0/argon2_cffi-23.1.0-py3-none-any.whl", hash = "sha256:c670642b78ba29641818ab2e68bd4e6a78ba53b7eff7b4c3815ae16abf91c7ea" },
]

[[package]]
name = "argon2-cffi-bindings"
version = "21.2.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "cffi" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/argon2-cffi-bindings/21.2.0/argon2-cffi-bindings-21.2.0.tar.gz", hash = "sha256:bb89ceffa6c791807d1305ceb77dbfacc5aa499891d2c55661c6459651fc39e3" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-macosx_10_9_x86_64.whl", hash = "sha256:ccb949252cb2ab3a08c02024acb77cfb179492d5701c7cbdbfd776124d4d2367" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:9524464572e12979364b7d600abf96181d3541da11e23ddf565a32e70bd4dc0d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:b746dba803a79238e925d9046a63aa26bf86ab2a2fe74ce6b009a1c3f5c8f2ae" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:58ed19212051f49a523abb1dbe954337dc82d947fb6e5a0da60f7c8471a8476c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-musllinux_1_1_aarch64.whl", hash = "sha256:bd46088725ef7f58b5a1ef7ca06647ebaf0eb4baff7d1d0d177c6cc8744abd86" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-musllinux_1_1_i686.whl", hash = "sha256:8cd69c07dd875537a824deec19f978e0f2078fdda07fd5c42ac29668dda5f40f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-musllinux_1_1_x86_64.whl", hash = "sha256:f1152ac548bd5b8bcecfb0b0371f082037e47128653df2e8ba6e914d384f3c3e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-win32.whl", hash = "sha256:603ca0aba86b1349b147cab91ae970c63118a0f30444d4bc80355937c950c082" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp36-abi3-win_amd64.whl", hash = "sha256:b2ef1c30440dbbcba7a5dc3e319408b59676e2e039e2ae11a8775ecf482b192f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/argon2-cffi-bindings/21.2.0/argon2_cffi_bindings-21.2.0-cp38-abi3-macosx_10_9_universal2.whl", hash = "sha256:e415e3f62c8d124ee16018e491a009937f8cf7ebf5eb430ffc5de21b900dad93" },
]

[[package]]
name = "asn1crypto"
version = "1.5.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/asn1crypto/1.5.1/asn1crypto-1.5.1.tar.gz", hash = "sha256:13ae38502be632115abf8a24cbe5f4da52e3b5231990aff31123c805306ccb9c" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/asn1crypto/1.5.1/asn1crypto-1.5.1-py2.py3-none-any.whl", hash = "sha256:db4e40728b728508912cbb3d44f19ce188f218e9eba635821bb4b68564f8fd67" },
]

[[package]]
name = "async-timeout"
version = "5.0.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/async-timeout/5.0.1/async_timeout-5.0.1.tar.gz", hash = "sha256:d9321a7a3d5a6a5e187e824d2fa0793ce379a202935782d555d6e9d2735677d3" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/async-timeout/5.0.1/async_timeout-5.0.1-py3-none-any.whl", hash = "sha256:39e3809566ff85354557ec2398b55e096c8364bacac9405a7a1fa429e77fe76c" },
]

[[package]]
name = "attrs"
version = "25.3.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/attrs/25.3.0/attrs-25.3.0.tar.gz", hash = "sha256:75d7cefc7fb576747b2c81b4442d4d4a1ce0900973527c011d1030fd3bf4af1b" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/attrs/25.3.0/attrs-25.3.0-py3-none-any.whl", hash = "sha256:427318ce031701fea540783410126f03899a97ffc6f61596ad581ac2e40e3bc3" },
]

[[package]]
name = "boto3"
version = "1.37.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "botocore" },
    { name = "jmespath" },
    { name = "s3transfer" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/boto3/1.37.1/boto3-1.37.1.tar.gz", hash = "sha256:96d18f7feb0c1fcb95f8837b74b6c8880e1b4e35ce5f8a8f8cb243a090c278ed" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/boto3/1.37.1/boto3-1.37.1-py3-none-any.whl", hash = "sha256:4320441f904435a1b85e6ecb81793192e522c737cc9ed6566014e29f0a11cb22" },
]

[[package]]
name = "botocore"
version = "1.37.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "jmespath" },
    { name = "python-dateutil" },
    { name = "urllib3" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/botocore/1.37.1/botocore-1.37.1.tar.gz", hash = "sha256:b194db8fb2a0ffba53568c364ae26166e7eec0445496b2ac86a6e142f3dd982f" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/botocore/1.37.1/botocore-1.37.1-py3-none-any.whl", hash = "sha256:c1db1bfc5d8c6b3b6d1ca6794f605294b4264e82a7e727b88e0fef9c2b9fbb9c" },
]

[[package]]
name = "cachetools"
version = "5.5.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cachetools/5.5.2/cachetools-5.5.2.tar.gz", hash = "sha256:1a661caa9175d26759571b2e19580f9d6393969e5dfca11fdb1f947a23e640d4" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cachetools/5.5.2/cachetools-5.5.2-py3-none-any.whl", hash = "sha256:d26a22bcc62eb95c3beabd9f1ee5e820d3d2704fe2967cbe350e20c8ffcd3f0a" },
]

[[package]]
name = "certifi"
version = "2025.1.31"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/certifi/2025.1.31/certifi-2025.1.31.tar.gz", hash = "sha256:3d5da6925056f6f18f119200434a4780a94263f10d1c21d032a6f6b2baa20651" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/certifi/2025.1.31/certifi-2025.1.31-py3-none-any.whl", hash = "sha256:ca78db4565a652026a4db2bcdf68f2fb589ea80d0be70e03929ed730746b84fe" },
]

[[package]]
name = "cffi"
version = "1.17.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "pycparser" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1.tar.gz", hash = "sha256:1c39c6016c32bc48dd54561950ebd6836e1670f2ae46128f67cf49e789c52824" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:a45e3c6913c5b87b3ff120dcdc03f6131fa0065027d0ed7ee6190736a74cd401" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:30c5e0cb5ae493c04c8b42916e52ca38079f1b235c2f8ae5f4527b963c401caf" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp311-cp311-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:f75c7ab1f9e4aca5414ed4d8e5c0e303a34f4421f8a0d47a4d019ceff0ab6af4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:a1ed2dd2972641495a3ec98445e09766f077aee98a1c896dcb4ad0d303628e41" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:46bf43160c1a35f7ec506d254e5c890f3c03648a4dbac12d624e4490a7046cd1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:a24ed04c8ffd54b0729c07cee15a81d964e6fee0e3d4d342a27b020d22959dc6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:610faea79c43e44c71e1ec53a554553fa22321b65fae24889706c0a84d4ad86d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp311-cp311-musllinux_1_1_aarch64.whl", hash = "sha256:a9b15d491f3ad5d692e11f6b71f7857e7835eb677955c00cc0aefcd0669adaf6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp311-cp311-musllinux_1_1_i686.whl", hash = "sha256:de2ea4b5833625383e464549fec1bc395c1bdeeb5f25c4a3a82b5a8c756ec22f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp311-cp311-musllinux_1_1_x86_64.whl", hash = "sha256:fc48c783f9c87e60831201f2cce7f3b2e4846bf4d8728eabe54d60700b318a0b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp311-cp311-win32.whl", hash = "sha256:85a950a4ac9c359340d5963966e3e0a94a676bd6245a4b55bc43949eee26a655" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp311-cp311-win_amd64.whl", hash = "sha256:caaf0640ef5f5517f49bc275eca1406b0ffa6aa184892812030f04c2abf589a0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:805b4371bf7197c329fcb3ead37e710d1bca9da5d583f5073b799d5c5bd1eee4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:733e99bc2df47476e3848417c5a4540522f234dfd4ef3ab7fafdf555b082ec0c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp312-cp312-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:1257bdabf294dceb59f5e70c64a3e2f462c30c7ad68092d01bbbfb1c16b1ba36" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:da95af8214998d77a98cc14e3a3bd00aa191526343078b530ceb0bd710fb48a5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:d63afe322132c194cf832bfec0dc69a99fb9bb6bbd550f161a49e9e855cc78ff" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:f79fc4fc25f1c8698ff97788206bb3c2598949bfe0fef03d299eb1b5356ada99" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:b62ce867176a75d03a665bad002af8e6d54644fad99a3c70905c543130e39d93" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:386c8bf53c502fff58903061338ce4f4950cbdcb23e2902d86c0f722b786bbe3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:4ceb10419a9adf4460ea14cfd6bc43d08701f0835e979bf821052f1805850fe8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp312-cp312-win32.whl", hash = "sha256:a08d7e755f8ed21095a310a693525137cfe756ce62d066e53f502a83dc550f65" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp312-cp312-win_amd64.whl", hash = "sha256:51392eae71afec0d0c8fb1a53b204dbb3bcabcb3c9b807eedf3e1e6ccf2de903" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:f3a2b4222ce6b60e2e8b337bb9596923045681d71e5a082783484d845390938e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:0984a4925a435b1da406122d4d7968dd861c1385afe3b45ba82b750f229811e2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp313-cp313-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:d01b12eeeb4427d3110de311e1774046ad344f5b1a7403101878976ecd7a10f3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:706510fe141c86a69c8ddc029c7910003a17353970cff3b904ff0686a5927683" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:de55b766c7aa2e2a3092c51e0483d700341182f08e67c63630d5b6f200bb28e5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:c59d6e989d07460165cc5ad3c61f9fd8f1b4796eacbd81cee78957842b834af4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:dd398dbc6773384a17fe0d3e7eeb8d1a21c2200473ee6806bb5e6a8e62bb73dd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp313-cp313-musllinux_1_1_aarch64.whl", hash = "sha256:3edc8d958eb099c634dace3c7e16560ae474aa3803a5df240542b305d14e14ed" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp313-cp313-musllinux_1_1_x86_64.whl", hash = "sha256:72e72408cad3d5419375fc87d289076ee319835bdfa2caad331e377589aebba9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp313-cp313-win32.whl", hash = "sha256:e03eab0a8677fa80d646b5ddece1cbeaf556c313dcfac435ba11f107ba117b5d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cffi/1.17.1/cffi-1.17.1-cp313-cp313-win_amd64.whl", hash = "sha256:f6a16c31041f09ead72d69f583767292f750d24913dadacf5756b966aacb3f1a" },
]

[[package]]
name = "charset-normalizer"
version = "3.4.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1.tar.gz", hash = "sha256:44251f18cd68a75b56585dd00dae26183e102cd5e0f9f1466e6df5da2ed64ea3" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-macosx_10_9_universal2.whl", hash = "sha256:8bfa33f4f2672964266e940dd22a195989ba31669bd84629f05fab3ef4e2d125" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:28bf57629c75e810b6ae989f03c0828d64d6b26a5e205535585f96093e405ed1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:f08ff5e948271dc7e18a35641d2f11a4cd8dfd5634f55228b691e62b37125eb3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:234ac59ea147c59ee4da87a0c0f098e9c8d169f4dc2a159ef720f1a61bbe27cd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:fd4ec41f914fa74ad1b8304bbc634b3de73d2a0889bd32076342a573e0779e00" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:eea6ee1db730b3483adf394ea72f808b6e18cf3cb6454b4d86e04fa8c4327a12" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:c96836c97b1238e9c9e3fe90844c947d5afbf4f4c92762679acfe19927d81d77" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:4d86f7aff21ee58f26dcf5ae81a9addbd914115cdebcbb2217e4f0ed8982e146" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-musllinux_1_2_ppc64le.whl", hash = "sha256:09b5e6733cbd160dcc09589227187e242a30a49ca5cefa5a7edd3f9d19ed53fd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-musllinux_1_2_s390x.whl", hash = "sha256:5777ee0881f9499ed0f71cc82cf873d9a0ca8af166dfa0af8ec4e675b7df48e6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:237bdbe6159cff53b4f24f397d43c6336c6b0b42affbe857970cefbb620911c8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-win32.whl", hash = "sha256:8417cb1f36cc0bc7eaba8ccb0e04d55f0ee52df06df3ad55259b9a323555fc8b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-win_amd64.whl", hash = "sha256:d7f50a1f8c450f3925cb367d011448c39239bb3eb4117c36a6d354794de4ce76" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:73d94b58ec7fecbc7366247d3b0b10a21681004153238750bb67bd9012414545" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:dad3e487649f498dd991eeb901125411559b22e8d7ab25d3aeb1af367df5efd7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:c30197aa96e8eed02200a83fba2657b4c3acd0f0aa4bdc9f6c1af8e8962e0757" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:2369eea1ee4a7610a860d88f268eb39b95cb588acd7235e02fd5a5601773d4fa" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:bc2722592d8998c870fa4e290c2eec2c1569b87fe58618e67d38b4665dfa680d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:ffc9202a29ab3920fa812879e95a9e78b2465fd10be7fcbd042899695d75e616" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:804a4d582ba6e5b747c625bf1255e6b1507465494a40a2130978bda7b932c90b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:0f55e69f030f7163dffe9fd0752b32f070566451afe180f99dbeeb81f511ad8d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:c4c3e6da02df6fa1410a7680bd3f63d4f710232d3139089536310d027950696a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:5df196eb874dae23dcfb968c83d4f8fdccb333330fe1fc278ac5ceeb101003a9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:e358e64305fe12299a08e08978f51fc21fac060dcfcddd95453eabe5b93ed0e1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-win32.whl", hash = "sha256:9b23ca7ef998bc739bf6ffc077c2116917eabcc901f88da1b9856b210ef63f35" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-win_amd64.whl", hash = "sha256:6ff8a4a60c227ad87030d76e99cd1698345d4491638dfa6673027c48b3cd395f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:aabfa34badd18f1da5ec1bc2715cadc8dca465868a4e73a0173466b688f29dda" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:22e14b5d70560b8dd51ec22863f370d1e595ac3d024cb8ad7d308b4cd95f8313" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:8436c508b408b82d87dc5f62496973a1805cd46727c34440b0d29d8a2f50a6c9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:2d074908e1aecee37a7635990b2c6d504cd4766c7bc9fc86d63f9c09af3fa11b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:955f8851919303c92343d2f66165294848d57e9bba6cf6e3625485a70a038d11" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:44ecbf16649486d4aebafeaa7ec4c9fed8b88101f4dd612dcaf65d5e815f837f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:0924e81d3d5e70f8126529951dac65c1010cdf117bb75eb02dd12339b57749dd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:2967f74ad52c3b98de4c3b32e1a44e32975e008a9cd2a8cc8966d6a5218c5cb2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:c75cb2a3e389853835e84a2d8fb2b81a10645b503eca9bcb98df6b5a43eb8886" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:09b26ae6b1abf0d27570633b2b078a2a20419c99d66fb2823173d73f188ce601" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:fa88b843d6e211393a37219e6a1c1df99d35e8fd90446f1118f4216e307e48cd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-win32.whl", hash = "sha256:eb8178fe3dba6450a3e024e95ac49ed3400e506fd4e9e5c32d30adda88cbd407" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-win_amd64.whl", hash = "sha256:b1ac5992a838106edb89654e0aebfc24f5848ae2547d22c2c3f66454daa11971" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-py3-none-any.whl", hash = "sha256:d98b1668f06378c6dbefec3b92299716b931cd4e6061f3c875a71ced1780ab85" },
]

[[package]]
name = "colorama"
version = "0.4.6"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/colorama/0.4.6/colorama-0.4.6.tar.gz", hash = "sha256:08695f5cb7ed6e0531a20572697297273c47b8cae5a63ffc6d6ed5c201be6e44" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/colorama/0.4.6/colorama-0.4.6-py2.py3-none-any.whl", hash = "sha256:4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6" },
]

[[package]]
name = "cryptography"
version = "44.0.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "cffi", marker = "platform_python_implementation != 'PyPy'" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2.tar.gz", hash = "sha256:c63454aa261a0cf0c5b4718349629793e9e634993538db841165b3df74f37ec0" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp37-abi3-macosx_10_9_universal2.whl", hash = "sha256:efcfe97d1b3c79e486554efddeb8f6f53a4cdd4cf6086642784fa31fc384e1d7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:29ecec49f3ba3f3849362854b7253a9f59799e3763b0c9d0826259a88efa02f1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:bc821e161ae88bfe8088d11bb39caf2916562e0a2dc7b6d56714a48b784ef0bb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp37-abi3-manylinux_2_28_aarch64.whl", hash = "sha256:3c00b6b757b32ce0f62c574b78b939afab9eecaf597c4d624caca4f9e71e7843" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp37-abi3-manylinux_2_28_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:7bdcd82189759aba3816d1f729ce42ffded1ac304c151d0a8e89b9996ab863d5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp37-abi3-manylinux_2_28_x86_64.whl", hash = "sha256:4973da6ca3db4405c54cd0b26d328be54c7747e89e284fcff166132eb7bccc9c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp37-abi3-manylinux_2_34_aarch64.whl", hash = "sha256:4e389622b6927d8133f314949a9812972711a111d577a5d1f4bee5e58736b80a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp37-abi3-manylinux_2_34_x86_64.whl", hash = "sha256:f514ef4cd14bb6fb484b4a60203e912cfcb64f2ab139e88c2274511514bf7308" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp37-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:1bc312dfb7a6e5d66082c87c34c8a62176e684b6fe3d90fcfe1568de675e6688" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp37-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:3b721b8b4d948b218c88cb8c45a01793483821e709afe5f622861fc6182b20a7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp37-abi3-win32.whl", hash = "sha256:51e4de3af4ec3899d6d178a8c005226491c27c4ba84101bfb59c901e10ca9f79" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp37-abi3-win_amd64.whl", hash = "sha256:c505d61b6176aaf982c5717ce04e87da5abc9a36a5b39ac03905c4aafe8de7aa" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp39-abi3-macosx_10_9_universal2.whl", hash = "sha256:8e0ddd63e6bf1161800592c71ac794d3fb8001f2caebe0966e77c5234fa9efc3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp39-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:81276f0ea79a208d961c433a947029e1a15948966658cf6710bbabb60fcc2639" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:9a1e657c0f4ea2a23304ee3f964db058c9e9e635cc7019c4aa21c330755ef6fd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp39-abi3-manylinux_2_28_aarch64.whl", hash = "sha256:6210c05941994290f3f7f175a4a57dbbb2afd9273657614c506d5976db061181" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp39-abi3-manylinux_2_28_armv7l.manylinux_2_31_armv7l.whl", hash = "sha256:d1c3572526997b36f245a96a2b1713bf79ce99b271bbcf084beb6b9b075f29ea" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp39-abi3-manylinux_2_28_x86_64.whl", hash = "sha256:b042d2a275c8cee83a4b7ae30c45a15e6a4baa65a179a0ec2d78ebb90e4f6699" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp39-abi3-manylinux_2_34_aarch64.whl", hash = "sha256:d03806036b4f89e3b13b6218fefea8d5312e450935b1a2d55f0524e2ed7c59d9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp39-abi3-manylinux_2_34_x86_64.whl", hash = "sha256:c7362add18b416b69d58c910caa217f980c5ef39b23a38a0880dfd87bdf8cd23" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp39-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:8cadc6e3b5a1f144a039ea08a0bdb03a2a92e19c46be3285123d32029f40a922" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp39-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:6f101b1f780f7fc613d040ca4bdf835c6ef3b00e9bd7125a4255ec574c7916e4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp39-abi3-win32.whl", hash = "sha256:3dc62975e31617badc19a906481deacdeb80b4bb454394b4098e3f2525a488c5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-cp39-abi3-win_amd64.whl", hash = "sha256:5f6f90b72d8ccadb9c6e311c775c8305381db88374c65fa1a68250aa8a9cb3a6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-pp311-pypy311_pp73-manylinux_2_28_aarch64.whl", hash = "sha256:909c97ab43a9c0c0b0ada7a1281430e4e5ec0458e6d9244c0e821bbf152f061d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-pp311-pypy311_pp73-manylinux_2_28_x86_64.whl", hash = "sha256:96e7a5e9d6e71f9f4fca8eebfd603f8e86c5225bb18eb621b2c1e50b290a9471" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-pp311-pypy311_pp73-manylinux_2_34_aarch64.whl", hash = "sha256:d1b3031093a366ac767b3feb8bcddb596671b3aaff82d4050f984da0c248b615" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cryptography/44.0.2/cryptography-44.0.2-pp311-pypy311_pp73-manylinux_2_34_x86_64.whl", hash = "sha256:04abd71114848aa25edb28e225ab5f268096f44cf0127f3d36975bdf1bdf3390" },
]

[[package]]
name = "dacite"
version = "1.9.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/dacite/1.9.2/dacite-1.9.2.tar.gz", hash = "sha256:6ccc3b299727c7aa17582f0021f6ae14d5de47c7227932c47fec4cdfefd26f09" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/dacite/1.9.2/dacite-1.9.2-py3-none-any.whl", hash = "sha256:053f7c3f5128ca2e9aceb66892b1a3c8936d02c686e707bee96e19deef4bc4a0" },
]

[[package]]
name = "docker"
version = "7.1.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "pywin32", marker = "sys_platform == 'win32'" },
    { name = "requests" },
    { name = "urllib3" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/docker/7.1.0/docker-7.1.0.tar.gz", hash = "sha256:ad8c70e6e3f8926cb8a92619b832b4ea5299e2831c14284663184e200546fa6c" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/docker/7.1.0/docker-7.1.0-py3-none-any.whl", hash = "sha256:c96b93b7f0a746f9e77d325bcfb87422a3d8bd4f03136ae8a85b37f1898d5fc0" },
]

[[package]]
name = "fakeredis"
version = "2.28.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "redis" },
    { name = "sortedcontainers" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/fakeredis/2.28.0/fakeredis-2.28.0.tar.gz", hash = "sha256:4768a78240ca03af79f8922f0b6774efb52c6bf2d27d38a622da9a2a7e8abc8d" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/fakeredis/2.28.0/fakeredis-2.28.0-py3-none-any.whl", hash = "sha256:e19b2a72cb0d13edaae95637748cef569840866d0807d93f4d381571034d1388" },
]

[[package]]
name = "filelock"
version = "3.18.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/filelock/3.18.0/filelock-3.18.0.tar.gz", hash = "sha256:adbc88eabb99d2fec8c9c1b229b171f18afa655400173ddc653d5d01501fb9f2" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/filelock/3.18.0/filelock-3.18.0-py3-none-any.whl", hash = "sha256:c401f4f8377c4464e6db25fff06205fd89bdd83b65eb0488ed1b160f780e21de" },
]

[[package]]
name = "frozenlist"
version = "1.5.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0.tar.gz", hash = "sha256:81d5af29e61b9c8348e876d442253723928dce6433e0e76cd925cd83f1b4b817" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp311-cp311-macosx_10_9_universal2.whl", hash = "sha256:fd74520371c3c4175142d02a976aee0b4cb4a7cc912a60586ffd8d5929979b30" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:2f3f7a0fbc219fb4455264cae4d9f01ad41ae6ee8524500f381de64ffaa077d5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:f47c9c9028f55a04ac254346e92977bf0f166c483c74b4232bee19a6697e4778" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:0996c66760924da6e88922756d99b47512a71cfd45215f3570bf1e0b694c206a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:a2fe128eb4edeabe11896cb6af88fca5346059f6c8d807e3b910069f39157869" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:1a8ea951bbb6cacd492e3948b8da8c502a3f814f5d20935aae74b5df2b19cf3d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:de537c11e4aa01d37db0d403b57bd6f0546e71a82347a97c6a9f0dcc532b3a45" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:9c2623347b933fcb9095841f1cc5d4ff0b278addd743e0e966cb3d460278840d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:cee6798eaf8b1416ef6909b06f7dc04b60755206bddc599f52232606e18179d3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:f5f9da7f5dbc00a604fe74aa02ae7c98bcede8a3b8b9666f9f86fc13993bc71a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp311-cp311-musllinux_1_2_ppc64le.whl", hash = "sha256:90646abbc7a5d5c7c19461d2e3eeb76eb0b204919e6ece342feb6032c9325ae9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp311-cp311-musllinux_1_2_s390x.whl", hash = "sha256:bdac3c7d9b705d253b2ce370fde941836a5f8b3c5c2b8fd70940a3ea3af7f4f2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:03d33c2ddbc1816237a67f66336616416e2bbb6beb306e5f890f2eb22b959cdf" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp311-cp311-win32.whl", hash = "sha256:237f6b23ee0f44066219dae14c70ae38a63f0440ce6750f868ee08775073f942" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp311-cp311-win_amd64.whl", hash = "sha256:0cc974cc93d32c42e7b0f6cf242a6bd941c57c61b618e78b6c0a96cb72788c1d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:31115ba75889723431aa9a4e77d5f398f5cf976eea3bdf61749731f62d4a4a21" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:7437601c4d89d070eac8323f121fcf25f88674627505334654fd027b091db09d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:7948140d9f8ece1745be806f2bfdf390127cf1a763b925c4a805c603df5e697e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:feeb64bc9bcc6b45c6311c9e9b99406660a9c05ca8a5b30d14a78555088b0b3a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:683173d371daad49cffb8309779e886e59c2f369430ad28fe715f66d08d4ab1a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:7d57d8f702221405a9d9b40f9da8ac2e4a1a8b5285aac6100f3393675f0a85ee" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:30c72000fbcc35b129cb09956836c7d7abf78ab5416595e4857d1cae8d6251a6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:000a77d6034fbad9b6bb880f7ec073027908f1b40254b5d6f26210d2dab1240e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:5d7f5a50342475962eb18b740f3beecc685a15b52c91f7d975257e13e029eca9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:87f724d055eb4785d9be84e9ebf0f24e392ddfad00b3fe036e43f489fafc9039" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:6e9080bb2fb195a046e5177f10d9d82b8a204c0736a97a153c2466127de87784" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:9b93d7aaa36c966fa42efcaf716e6b3900438632a626fb09c049f6a2f09fc631" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:52ef692a4bc60a6dd57f507429636c2af8b6046db8b31b18dac02cbc8f507f7f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp312-cp312-win32.whl", hash = "sha256:29d94c256679247b33a3dc96cce0f93cbc69c23bf75ff715919332fdbb6a32b8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp312-cp312-win_amd64.whl", hash = "sha256:8969190d709e7c48ea386db202d708eb94bdb29207a1f269bab1196ce0dcca1f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:7a1a048f9215c90973402e26c01d1cff8a209e1f1b53f72b95c13db61b00f953" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:dd47a5181ce5fcb463b5d9e17ecfdb02b678cca31280639255ce9d0e5aa67af0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:1431d60b36d15cda188ea222033eec8e0eab488f39a272461f2e6d9e1a8e63c2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:6482a5851f5d72767fbd0e507e80737f9c8646ae7fd303def99bfe813f76cf7f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:44c49271a937625619e862baacbd037a7ef86dd1ee215afc298a417ff3270608" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:12f78f98c2f1c2429d42e6a485f433722b0061d5c0b0139efa64f396efb5886b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:ce3aa154c452d2467487765e3adc730a8c153af77ad84096bc19ce19a2400840" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp313-cp313-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:9b7dc0c4338e6b8b091e8faf0db3168a37101943e687f373dce00959583f7439" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:45e0896250900b5aa25180f9aec243e84e92ac84bd4a74d9ad4138ef3f5c97de" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:561eb1c9579d495fddb6da8959fd2a1fca2c6d060d4113f5844b433fc02f2641" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:df6e2f325bfee1f49f81aaac97d2aa757c7646534a06f8f577ce184afe2f0a9e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:140228863501b44b809fb39ec56b5d4071f4d0aa6d216c19cbb08b8c5a7eadb9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:7707a25d6a77f5d27ea7dc7d1fc608aa0a478193823f88511ef5e6b8a48f9d03" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp313-cp313-win32.whl", hash = "sha256:31a9ac2b38ab9b5a8933b693db4939764ad3f299fcaa931a3e605bc3460e693c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-cp313-cp313-win_amd64.whl", hash = "sha256:11aabdd62b8b9c4b84081a3c246506d1cddd2dd93ff0ad53ede5defec7886b28" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/frozenlist/1.5.0/frozenlist-1.5.0-py3-none-any.whl", hash = "sha256:d994863bba198a4a518b467bb971c56e1db3f180a25c6cf7bb1949c267f748c3" },
]

[[package]]
name = "gametime-protos"
version = "1.8.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "google-api-core" },
    { name = "grpcio" },
    { name = "protobuf" },
    { name = "types-protobuf" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/gametime-protos/1.8.0/gametime_protos-1.8.0.tar.gz", hash = "sha256:4710870df5ac06d5ea6c15e073c756681092fa5c0ca4695b2795860c9583b594" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/gametime-protos/1.8.0/gametime_protos-1.8.0-py3-none-any.whl", hash = "sha256:d78b82509456a84b63c40dbfc620f5e184371f861e228016e3622e8984330d88" },
]

[[package]]
name = "google-api-core"
version = "2.24.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "google-auth" },
    { name = "googleapis-common-protos" },
    { name = "proto-plus" },
    { name = "protobuf" },
    { name = "requests" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/google-api-core/2.24.2/google_api_core-2.24.2.tar.gz", hash = "sha256:81718493daf06d96d6bc76a91c23874dbf2fac0adbbf542831b805ee6e974696" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/google-api-core/2.24.2/google_api_core-2.24.2-py3-none-any.whl", hash = "sha256:810a63ac95f3c441b7c0e43d344e372887f62ce9071ba972eacf32672e072de9" },
]

[[package]]
name = "google-auth"
version = "2.38.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "cachetools" },
    { name = "pyasn1-modules" },
    { name = "rsa" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/google-auth/2.38.0/google_auth-2.38.0.tar.gz", hash = "sha256:8285113607d3b80a3f1543b75962447ba8a09fe85783432a784fdeef6ac094c4" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/google-auth/2.38.0/google_auth-2.38.0-py2.py3-none-any.whl", hash = "sha256:e7dae6694313f434a2727bf2906f27ad259bae090d7aa896590d86feec3d9d4a" },
]

[[package]]
name = "googleapis-common-protos"
version = "1.69.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "protobuf" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/googleapis-common-protos/1.69.2/googleapis_common_protos-1.69.2.tar.gz", hash = "sha256:3e1b904a27a33c821b4b749fd31d334c0c9c30e6113023d495e48979a3dc9c5f" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/googleapis-common-protos/1.69.2/googleapis_common_protos-1.69.2-py3-none-any.whl", hash = "sha256:0b30452ff9c7a27d80bfc5718954063e8ab53dd3697093d3bc99581f5fd24212" },
]

[[package]]
name = "grpcio"
version = "1.71.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0.tar.gz", hash = "sha256:2b85f7820475ad3edec209d3d89a7909ada16caab05d3f2e08a7e8ae3200a55c" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-linux_armv7l.whl", hash = "sha256:d6aa986318c36508dc1d5001a3ff169a15b99b9f96ef5e98e13522c506b37eef" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-macosx_10_14_universal2.whl", hash = "sha256:d2c170247315f2d7e5798a22358e982ad6eeb68fa20cf7a820bb74c11f0736e7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-manylinux_2_17_aarch64.whl", hash = "sha256:e6f83a583ed0a5b08c5bc7a3fe860bb3c2eac1f03f1f63e0bc2091325605d2b7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:4be74ddeeb92cc87190e0e376dbc8fc7736dbb6d3d454f2fa1f5be1dee26b9d7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:4dd0dfbe4d5eb1fcfec9490ca13f82b089a309dc3678e2edabc144051270a66e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-musllinux_1_1_aarch64.whl", hash = "sha256:a2242d6950dc892afdf9e951ed7ff89473aaf744b7d5727ad56bdaace363722b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-musllinux_1_1_i686.whl", hash = "sha256:0fa05ee31a20456b13ae49ad2e5d585265f71dd19fbd9ef983c28f926d45d0a7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-musllinux_1_1_x86_64.whl", hash = "sha256:3d081e859fb1ebe176de33fc3adb26c7d46b8812f906042705346b314bde32c3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-win32.whl", hash = "sha256:d6de81c9c00c8a23047136b11794b3584cdc1460ed7cbc10eada50614baa1444" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-win_amd64.whl", hash = "sha256:24e867651fc67717b6f896d5f0cac0ec863a8b5fb7d6441c2ab428f52c651c6b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-linux_armv7l.whl", hash = "sha256:0ff35c8d807c1c7531d3002be03221ff9ae15712b53ab46e2a0b4bb271f38537" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-macosx_10_14_universal2.whl", hash = "sha256:b78a99cd1ece4be92ab7c07765a0b038194ded2e0a26fd654591ee136088d8d7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-manylinux_2_17_aarch64.whl", hash = "sha256:dc1a1231ed23caac1de9f943d031f1bc38d0f69d2a3b243ea0d664fc1fbd7fec" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:e6beeea5566092c5e3c4896c6d1d307fb46b1d4bdf3e70c8340b190a69198594" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:d5170929109450a2c031cfe87d6716f2fae39695ad5335d9106ae88cc32dc84c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:5b08d03ace7aca7b2fadd4baf291139b4a5f058805a8327bfe9aece7253b6d67" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-musllinux_1_1_i686.whl", hash = "sha256:f903017db76bf9cc2b2d8bdd37bf04b505bbccad6be8a81e1542206875d0e9db" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:469f42a0b410883185eab4689060a20488a1a0a00f8bbb3cbc1061197b4c5a79" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-win32.whl", hash = "sha256:ad9f30838550695b5eb302add33f21f7301b882937460dd24f24b3cc5a95067a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-win_amd64.whl", hash = "sha256:652350609332de6dac4ece254e5d7e1ff834e203d6afb769601f286886f6f3a8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-linux_armv7l.whl", hash = "sha256:cebc1b34ba40a312ab480ccdb396ff3c529377a2fce72c45a741f7215bfe8379" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-macosx_10_14_universal2.whl", hash = "sha256:85da336e3649a3d2171e82f696b5cad2c6231fdd5bad52616476235681bee5b3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-manylinux_2_17_aarch64.whl", hash = "sha256:f9a412f55bb6e8f3bb000e020dbc1e709627dcb3a56f6431fa7076b4c1aab0db" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:47be9584729534660416f6d2a3108aaeac1122f6b5bdbf9fd823e11fe6fbaa29" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:7c9c80ac6091c916db81131d50926a93ab162a7e97e4428ffc186b6e80d6dda4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-musllinux_1_1_aarch64.whl", hash = "sha256:789d5e2a3a15419374b7b45cd680b1e83bbc1e52b9086e49308e2c0b5bbae6e3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-musllinux_1_1_i686.whl", hash = "sha256:1be857615e26a86d7363e8a163fade914595c81fec962b3d514a4b1e8760467b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-musllinux_1_1_x86_64.whl", hash = "sha256:a76d39b5fafd79ed604c4be0a869ec3581a172a707e2a8d7a4858cb05a5a7637" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-win32.whl", hash = "sha256:74258dce215cb1995083daa17b379a1a5a87d275387b7ffe137f1d5131e2cfbb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-win_amd64.whl", hash = "sha256:22c3bc8d488c039a199f7a003a38cb7635db6656fa96437a8accde8322ce2366" },
]

[[package]]
name = "grpcio-health-checking"
version = "1.71.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "grpcio" },
    { name = "protobuf" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio-health-checking/1.71.0/grpcio_health_checking-1.71.0.tar.gz", hash = "sha256:ff9bd55beb97ce3322fda2ae58781c9d6c6fcca6a35ca3b712975d9f75dd30af" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio-health-checking/1.71.0/grpcio_health_checking-1.71.0-py3-none-any.whl", hash = "sha256:b7d9b7a7606ab4cd02d23bd1d3943843f784ffc987c9bfec14c9d058d9e279db" },
]

[[package]]
name = "idna"
version = "3.10"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/idna/3.10/idna-3.10.tar.gz", hash = "sha256:12f65c9b470abda6dc35cf8e63cc574b1c52b11df2c86030af0ac09b01b13ea9" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/idna/3.10/idna-3.10-py3-none-any.whl", hash = "sha256:946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3" },
]

[[package]]
name = "iniconfig"
version = "2.1.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/iniconfig/2.1.0/iniconfig-2.1.0.tar.gz", hash = "sha256:3abbd2e30b36733fee78f9c7f7308f2d0050e88f0087fd25c2645f63c773e1c7" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/iniconfig/2.1.0/iniconfig-2.1.0-py3-none-any.whl", hash = "sha256:9deba5723312380e77435581c6bf4935c94cbfab9b1ed33ef8d238ea168eb760" },
]

[[package]]
name = "jinja2"
version = "3.1.6"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "markupsafe" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/jinja2/3.1.6/jinja2-3.1.6.tar.gz", hash = "sha256:0137fb05990d35f1275a587e9aee6d56da821fc83491a0fb838183be43f66d6d" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/jinja2/3.1.6/jinja2-3.1.6-py3-none-any.whl", hash = "sha256:85ece4451f492d0c13c5dd7c13a64681a86afae63a5f347908daf103ce6d2f67" },
]

[[package]]
name = "jmespath"
version = "1.0.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/jmespath/1.0.1/jmespath-1.0.1.tar.gz", hash = "sha256:90261b206d6defd58fdd5e85f478bf633a2901798906be2ad389150c5c60edbe" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/jmespath/1.0.1/jmespath-1.0.1-py3-none-any.whl", hash = "sha256:02e2e4cc71b5bcab88332eebf907519190dd9e6e82107fa7f83b1003a6252980" },
]

[[package]]
name = "joblib"
version = "1.4.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/joblib/1.4.2/joblib-1.4.2.tar.gz", hash = "sha256:2382c5816b2636fbd20a09e0f4e9dad4736765fdfb7dca582943b9c1366b3f0e" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/joblib/1.4.2/joblib-1.4.2-py3-none-any.whl", hash = "sha256:06d478d5674cbc267e7496a410ee875abd68e4340feff4490bcb7afb88060ae6" },
]

[[package]]
name = "kafka-python"
version = "2.1.4"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/kafka-python/2.1.4/kafka_python-2.1.4.tar.gz", hash = "sha256:7e45be44cc7eb3dcad8b432cc514411454d05971ad26411092577584ee15d67a" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/kafka-python/2.1.4/kafka_python-2.1.4-py2.py3-none-any.whl", hash = "sha256:23340cdcd403d4f73564c068fd3b1f4b4be3fc1a89f3ef1a76d46ea889878190" },
]

[[package]]
name = "markupsafe"
version = "3.0.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/markupsafe-3.0.2.tar.gz", hash = "sha256:ee55d3edf80167e48ea11a923c7386f4669df67d7994554387f84e7d8b0a2bf0" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp311-cp311-macosx_10_9_universal2.whl", hash = "sha256:9025b4018f3a1314059769c7bf15441064b2207cb3f065e6ea1e7359cb46db9d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:93335ca3812df2f366e80509ae119189886b0f3c2b81325d39efdb84a1e2ae93" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:2cb8438c3cbb25e220c2ab33bb226559e7afb3baec11c4f218ffa7308603c832" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:a123e330ef0853c6e822384873bef7507557d8e4a082961e1defa947aa59ba84" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:1e084f686b92e5b83186b07e8a17fc09e38fff551f3602b249881fec658d3eca" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:d8213e09c917a951de9d09ecee036d5c7d36cb6cb7dbaece4c71a60d79fb9798" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:5b02fb34468b6aaa40dfc198d813a641e3a63b98c2b05a16b9f80b7ec314185e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:0bff5e0ae4ef2e1ae4fdf2dfd5b76c75e5c2fa4132d05fc1b0dabcd20c7e28c4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp311-cp311-win32.whl", hash = "sha256:6c89876f41da747c8d3677a2b540fb32ef5715f97b66eeb0c6b66f5e3ef6f59d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp311-cp311-win_amd64.whl", hash = "sha256:70a87b411535ccad5ef2f1df5136506a10775d267e197e4cf531ced10537bd6b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:9778bd8ab0a994ebf6f84c2b949e65736d5575320a17ae8984a77fab08db94cf" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:846ade7b71e3536c4e56b386c2a47adf5741d2d8b94ec9dc3e92e5e1ee1e2225" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1c99d261bd2d5f6b59325c92c73df481e05e57f19837bdca8413b9eac4bd8028" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:e17c96c14e19278594aa4841ec148115f9c7615a47382ecb6b82bd8fea3ab0c8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:88416bd1e65dcea10bc7569faacb2c20ce071dd1f87539ca2ab364bf6231393c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:2181e67807fc2fa785d0592dc2d6206c019b9502410671cc905d132a92866557" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:52305740fe773d09cffb16f8ed0427942901f00adedac82ec8b67752f58a1b22" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:ad10d3ded218f1039f11a75f8091880239651b52e9bb592ca27de44eed242a48" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp312-cp312-win32.whl", hash = "sha256:0f4ca02bea9a23221c0182836703cbf8930c5e9454bacce27e767509fa286a30" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp312-cp312-win_amd64.whl", hash = "sha256:8e06879fc22a25ca47312fbe7c8264eb0b662f6db27cb2d3bbbc74b1df4b9b87" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:ba9527cdd4c926ed0760bc301f6728ef34d841f405abf9d4f959c478421e4efd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:f8b3d067f2e40fe93e1ccdd6b2e1d16c43140e76f02fb1319a05cf2b79d99430" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:569511d3b58c8791ab4c2e1285575265991e6d8f8700c7be0e88f86cb0672094" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:15ab75ef81add55874e7ab7055e9c397312385bd9ced94920f2802310c930396" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:f3818cb119498c0678015754eba762e0d61e5b52d34c8b13d770f0719f7b1d79" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:cdb82a876c47801bb54a690c5ae105a46b392ac6099881cdfb9f6e95e4014c6a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:cabc348d87e913db6ab4aa100f01b08f481097838bdddf7c7a84b7575b7309ca" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:444dcda765c8a838eaae23112db52f1efaf750daddb2d9ca300bcae1039adc5c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313-win32.whl", hash = "sha256:bcf3e58998965654fdaff38e58584d8937aa3096ab5354d493c77d1fdd66d7a1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313-win_amd64.whl", hash = "sha256:e6a2a455bd412959b57a172ce6328d2dd1f01cb2135efda2e4576e8a23fa3b0f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:b5a6b3ada725cea8a5e634536b1b01c30bcdcd7f9c6fff4151548d5bf6b3a36c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:a904af0a6162c73e3edcb969eeeb53a63ceeb5d8cf642fade7d39e7963a22ddb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:4aa4e5faecf353ed117801a068ebab7b7e09ffb6e1d5e412dc852e0da018126c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:c0ef13eaeee5b615fb07c9a7dadb38eac06a0608b41570d8ade51c56539e509d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313t-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:d16a81a06776313e817c951135cf7340a3e91e8c1ff2fac444cfd75fffa04afe" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:6381026f158fdb7c72a168278597a5e3a5222e83ea18f543112b2662a9b699c5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313t-musllinux_1_2_i686.whl", hash = "sha256:3d79d162e7be8f996986c064d1c7c817f6df3a77fe3d6859f6f9e7be4b8c213a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:131a3c7689c85f5ad20f9f6fb1b866f402c445b220c19fe4308c0b147ccd2ad9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313t-win32.whl", hash = "sha256:ba8062ed2cf21c07a9e295d5b8a2a5ce678b913b45fdf68c32d95d6c1291e0b6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/markupsafe/3.0.2/MarkupSafe-3.0.2-cp313-cp313t-win_amd64.whl", hash = "sha256:e444a31f8db13eb18ada366ab3cf45fd4b31e4db1236a4448f68778c1d1a5a2f" },
]

[[package]]
name = "minio"
version = "7.2.15"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "argon2-cffi" },
    { name = "certifi" },
    { name = "pycryptodome" },
    { name = "typing-extensions" },
    { name = "urllib3" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/minio/7.2.15/minio-7.2.15.tar.gz", hash = "sha256:5247df5d4dca7bfa4c9b20093acd5ad43e82d8710ceb059d79c6eea970f49f79" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/minio/7.2.15/minio-7.2.15-py3-none-any.whl", hash = "sha256:c06ef7a43e5d67107067f77b6c07ebdd68733e5aa7eed03076472410ca19d876" },
]

[[package]]
name = "mlutils"
version = "1.3.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/mlutils/1.3.0/mlutils-1.3.0.tar.gz", hash = "sha256:5d61e17b06eefc840fb4362430bef0f70411c8257344af42c63d7cdda36a060e" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/mlutils/1.3.0/mlutils-1.3.0-py3-none-any.whl", hash = "sha256:acd3f78e42e0a6b374f22b8b64ae1519d3e78fe13cf0b942f9780d4a73f04f7c" },
]

[package.optional-dependencies]
aiohttp = [
    { name = "aiohttp" },
]
grpc = [
    { name = "grpcio" },
    { name = "grpcio-health-checking" },
]
pandas = [
    { name = "pandas" },
]
sklearn = [
    { name = "numpy" },
    { name = "pandas" },
    { name = "scikit-learn" },
]

[[package]]
name = "multidict"
version = "6.3.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0.tar.gz", hash = "sha256:2cf3e0781febf9f093eff3eca2d6dd7954ef2969ff46f6cd95173a4db8397fd8" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp311-cp311-macosx_10_9_universal2.whl", hash = "sha256:904895323a59dd50387cddd1337c3eac48ec0fe6cb4d2e4106dc1a3b80091727" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:1aa6e1a605c602c119a22368f7d3b9dfb102a7cd69edae5a89e55da34fd64c2b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:72574693fad34d075a54fa771984a1a8809a9eff880b731152078e81c7d8c80f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:72d15a61b76fa4b814390c21defd772893d1152157e3297e6afb328ef32d1711" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:7bf0d43f2c56b600c6895c25a420e4b46c6a5acf91d6c9a6300713496f70e63b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:6be1182387fb9a95fd8a8cbfab2f2b61d61ea0ab4699780d4300ea207c8f8d71" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:0ba7d12ff77cadb59519aba674cac3a0b16d0fc5abaa3ef79b5ff3806e52e991" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:eeca775446b00826ec8ca34eed8c902c6a1ae7dc42d78094b1f73ecd38d1dcf8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:0a1a5f50ccc57a317db68582411ce6aca076ee3bc386290b416a5783e807afa1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:6989ea9326f9a9171edc920a459003b1deffc6cb48605b8f2610846c7fbd201a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp311-cp311-musllinux_1_2_ppc64le.whl", hash = "sha256:9efe23b3d0671eeb8b1e70791612538dcdc1b39275563ca78f338aa0ce9619d2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp311-cp311-musllinux_1_2_s390x.whl", hash = "sha256:560af476d03256a65bd4ff05c83e7869ec3c2b40579d4ed1ac74075ef19e02bf" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:091f3dfe63e9f48ae6c7da8d638e8d8e050ff11e348f69bafc20e65d11f7aa48" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp311-cp311-win32.whl", hash = "sha256:38e60e400e07ba678a755cc89b09955a49acb0188d47aee2829ed4efc1ccb053" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp311-cp311-win_amd64.whl", hash = "sha256:65e763769cec2fa9c79b70555e3f2a7903747d127ab14ec9eaa5f00763bb4152" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:2a83896925e3a2ddec736c48255572e6f15a95339024c02b800dab28e63461a3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:74e45386528db2d9346618276e8746507ec6ccb91512c5f02d2049ccafb0576e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:bc15e81587b652bbeba9d4d7d3707bcaaa1d7b4aab2024c14be477662003a211" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:a716344c6e92551bdf842b999c7d7b34a13e32facf3e6c5942690c9883d45e3a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:299070f503a9a99e4af38d07da784ced28377cc62b678084b9e2e48fa51c57d3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:2e87a8635a7db577617b50bd2f2080744ed20e556750b97e4f9988e6d20d3941" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:8ab4ea5f49166b990411c522c1f5f901014932ead15a463616ec93e10fff2c05" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:2cef02d3878804909283549bc10d4789a14c610fcd286f17cd94a195b21fe469" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:a466c14574152b7caed5d76f1e46ae2963d33f5b0eb2dd915fa33870183b0f26" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:883c07b404229e98d30b1ef64f07d4e3422d431ecd727c2ebba8549f70b2ba16" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:767ed12b1662fac32d84eb43707e4f778b12554fc12ce3c8f7793a02111a9e32" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:34ef116022119d3053ecce8ba62109c8b54a650a537b183e79de1db4078894a8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:3ad737617d38c55c0b72cd3ea01bf5cbab0d75123de5e52c39643ddc6465cb5f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp312-cp312-win32.whl", hash = "sha256:3d783be54d076843f58bf061fdaf1071789fb924fb35a0eb84dbc2c8b68499a2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp312-cp312-win_amd64.whl", hash = "sha256:6fbe184451480c17f1f8dac160c9f3f6d243010fdb8416de4d3d7ee69ea65aa4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:90c3c60985e7da13e44aeaaf2e1c10fe1b7825788a18c82b0f9eaeb6c4e9d9c6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:80935f26af0eec490c4e52381a28f39b08c2bc4ef4562448890027e4a4cfa3a4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:e18db3b38ac30d8cc1ddbdfc7f6d3d814c1abb8936c57bd1c09c5da02873c8c4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1c8836280abc310ea6260dac93126645d21284885008c317c2ac4281a90f1398" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:5737f40ffb1926b8bf38d32fdac48a27c441b757f1bf44cbaa100dafef049a01" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:b6df1b707adab4858dfaa74877f60f07d9d6be01a57015cf4e62aa8f00f3576b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:162af9c2e0743465a47e2b32d9b0a7c260b7843629b5e6f0a8a92819b5a40d27" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:dc4e82c5db4e65703c842b0947699dd958a7262a8b854d1c19bbfe2d27be9333" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:5a922601cb94f427dd96eec3a7c776537ce7490f2beb69e88a81859e537343e4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:3da30fb560b37cede31306b890c982213a23fa7335d368cdc16cf7034170860b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:5a1996d963016e6486b6a672f64f868e6b4e7e9e2caf710994df11b04790903e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:9584441b7a603a12aa382adf8c093ddc5a22328a108dabc6a4a112fa5b4facae" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:71a8ce430f6c341725aefc0031626c484e0858fd95d1bf3625e0d27919d8edca" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313-win32.whl", hash = "sha256:b7d3053742a9a559dda8598a52e0c1bcbd18258cc199cba52137ce8c8e92c537" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313-win_amd64.whl", hash = "sha256:6b7e30e4246c2cd5e6c2c907486d235e4f3e8a39979744e9e0b8090629c62da4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:be034aa2841c57ea43a55bc123d8f3f41cc2d3d102a22390c863791ba6bf23f1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:168a02178b7e980899f3475ff008436eaab035d50e39cb8f7de641bbe9bbc3a6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:0c4a6ec469c30f4162745d6485b778432c497032b8a2cb3a0332eea9d3b6aef6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1b293804f4b53be6297bc2afdeaf15aff76c1b6be69f3a3da785143eebdfb656" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313t-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:5eb2a8415a891564054447a327a7ef7ec90e69e0e63d85d1ffb03f82e102c740" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313t-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:db61c6ae9ee7268dc69a078ea22deaaf861350ad2e4c194c70798b8ec9789131" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:88548dec2f6f69e547252fe3113bf1b3b08c0879f5b44633927be07ed18c5cc0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313t-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:864684b36e0488715aac531785abe7514e872bfe83ecc768828e9ddaadeed320" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:71d92e846541669ae1d11e00906c408c1dc9890196e13f11d4d62bd235ac9ddb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313t-musllinux_1_2_i686.whl", hash = "sha256:c5223ab45639ce93c7233d518f6c3199422b49dbd0ebfb1d7917b5da2636712e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313t-musllinux_1_2_ppc64le.whl", hash = "sha256:56225de73d69f5ee6b783648eb1936e1bbe874a529cb1e15d64038904c54efb2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313t-musllinux_1_2_s390x.whl", hash = "sha256:66c108d8e02da2febb0aa7d7002e14c4a0571460993c9edf8249393cdae7eeef" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:b95d96a0640decaa24cd9cf386fd4d8a96c973aafa42dd9c65609f9f0d66cc34" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313t-win32.whl", hash = "sha256:6b25953a1d6a97746becbd663b49e3b436a5001c995a62662d65835a2ba996a7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-cp313-cp313t-win_amd64.whl", hash = "sha256:d9c2b1ca98e5454b78cd434f29fc33eb8f8a2f343efc5f975225d92070b9f7f6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/multidict/6.3.0/multidict-6.3.0-py3-none-any.whl", hash = "sha256:9ca652d9c6f68535537d75502b549ed0ca07fa6d3908f84f29f92148ec7310f2" },
]

[[package]]
name = "numpy"
version = "2.2.4"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4.tar.gz", hash = "sha256:9ba03692a45d3eef66559efe1d1096c4b9b75c0986b5dff5530c378fb8331d4f" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:e9e0a277bb2eb5d8a7407e14688b85fd8ad628ee4e0c7930415687b6564207a4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:9eeea959168ea555e556b8188da5fa7831e21d91ce031e95ce23747b7609f8a4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp311-cp311-macosx_14_0_arm64.whl", hash = "sha256:bd3ad3b0a40e713fc68f99ecfd07124195333f1e689387c180813f0e94309d6f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp311-cp311-macosx_14_0_x86_64.whl", hash = "sha256:cf28633d64294969c019c6df4ff37f5698e8326db68cc2b66576a51fad634880" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:2fa8fa7697ad1646b5c93de1719965844e004fcad23c91228aca1cf0800044a1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f4162988a360a29af158aeb4a2f4f09ffed6a969c9776f8f3bdee9b06a8ab7e5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:892c10d6a73e0f14935c31229e03325a7b3093fafd6ce0af704be7f894d95687" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:db1f1c22173ac1c58db249ae48aa7ead29f534b9a948bc56828337aa84a32ed6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp311-cp311-win32.whl", hash = "sha256:ea2bb7e2ae9e37d96835b3576a4fa4b3a97592fbea8ef7c3587078b0068b8f09" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp311-cp311-win_amd64.whl", hash = "sha256:f7de08cbe5551911886d1ab60de58448c6df0f67d9feb7d1fb21e9875ef95e91" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:a7b9084668aa0f64e64bd00d27ba5146ef1c3a8835f3bd912e7a9e01326804c4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:dbe512c511956b893d2dacd007d955a3f03d555ae05cfa3ff1c1ff6df8851854" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp312-cp312-macosx_14_0_arm64.whl", hash = "sha256:bb649f8b207ab07caebba230d851b579a3c8711a851d29efe15008e31bb4de24" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp312-cp312-macosx_14_0_x86_64.whl", hash = "sha256:f34dc300df798742b3d06515aa2a0aee20941c13579d7a2f2e10af01ae4901ee" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:c3f7ac96b16955634e223b579a3e5798df59007ca43e8d451a0e6a50f6bfdfba" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:4f92084defa704deadd4e0a5ab1dc52d8ac9e8a8ef617f3fbb853e79b0ea3592" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:7a4e84a6283b36632e2a5b56e121961f6542ab886bc9e12f8f9818b3c266bfbb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:11c43995255eb4127115956495f43e9343736edb7fcdb0d973defd9de14cd84f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp312-cp312-win32.whl", hash = "sha256:65ef3468b53269eb5fdb3a5c09508c032b793da03251d5f8722b1194f1790c00" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp312-cp312-win_amd64.whl", hash = "sha256:2aad3c17ed2ff455b8eaafe06bcdae0062a1db77cb99f4b9cbb5f4ecb13c5146" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:1cf4e5c6a278d620dee9ddeb487dc6a860f9b199eadeecc567f777daace1e9e7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:1974afec0b479e50438fc3648974268f972e2d908ddb6d7fb634598cdb8260a0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313-macosx_14_0_arm64.whl", hash = "sha256:79bd5f0a02aa16808fcbc79a9a376a147cc1045f7dfe44c6e7d53fa8b8a79392" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313-macosx_14_0_x86_64.whl", hash = "sha256:3387dd7232804b341165cedcb90694565a6015433ee076c6754775e85d86f1fc" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:6f527d8fdb0286fd2fd97a2a96c6be17ba4232da346931d967a0630050dfd298" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:bce43e386c16898b91e162e5baaad90c4b06f9dcbe36282490032cec98dc8ae7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:31504f970f563d99f71a3512d0c01a645b692b12a63630d6aafa0939e52361e6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:81413336ef121a6ba746892fad881a83351ee3e1e4011f52e97fba79233611fd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313-win32.whl", hash = "sha256:f486038e44caa08dbd97275a9a35a283a8f1d2f0ee60ac260a1790e76660833c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313-win_amd64.whl", hash = "sha256:207a2b8441cc8b6a2a78c9ddc64d00d20c303d79fba08c577752f080c4007ee3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:8120575cb4882318c791f839a4fd66161a6fa46f3f0a5e613071aae35b5dd8f8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:a761ba0fa886a7bb33c6c8f6f20213735cb19642c580a931c625ee377ee8bd39" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313t-macosx_14_0_arm64.whl", hash = "sha256:ac0280f1ba4a4bfff363a99a6aceed4f8e123f8a9b234c89140f5e894e452ecd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313t-macosx_14_0_x86_64.whl", hash = "sha256:879cf3a9a2b53a4672a168c21375166171bc3932b7e21f622201811c43cdd3b0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:f05d4198c1bacc9124018109c5fba2f3201dbe7ab6e92ff100494f236209c960" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:e2f085ce2e813a50dfd0e01fbfc0c12bbe5d2063d99f8b29da30e544fb6483b8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:92bda934a791c01d6d9d8e038363c50918ef7c40601552a58ac84c9613a665bc" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:ee4d528022f4c5ff67332469e10efe06a267e32f4067dc76bb7e2cddf3cd25ff" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313t-win32.whl", hash = "sha256:05c076d531e9998e7e694c36e8b349969c56eadd2cdcd07242958489d79a7286" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/numpy/2.2.4/numpy-2.2.4-cp313-cp313t-win_amd64.whl", hash = "sha256:188dcbca89834cc2e14eb2f106c96d6d46f200fe0200310fc29089657379c58d" },
]

[[package]]
name = "packaging"
version = "24.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/packaging/24.2/packaging-24.2.tar.gz", hash = "sha256:c228a6dc5e932d346bc5739379109d49e8853dd8223571c7c5b55260edc0b97f" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/packaging/24.2/packaging-24.2-py3-none-any.whl", hash = "sha256:09abb1bccd265c01f4a3aa3f7a7db064b36514d2cba19a2f694fe6150451a759" },
]

[[package]]
name = "pandas"
version = "2.2.3"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "numpy" },
    { name = "python-dateutil" },
    { name = "pytz" },
    { name = "tzdata" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3.tar.gz", hash = "sha256:4f18ba62b61d7e192368b84517265a99b4d7ee8912f8708660fb4a366cc82667" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:66108071e1b935240e74525006034333f98bcdb87ea116de573a6a0dccb6c039" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:7c2875855b0ff77b2a64a0365e24455d9990730d6431b9e0ee18ad8acee13dbd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp311-cp311-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:cd8d0c3be0515c12fed0bdbae072551c8b54b7192c7b1fda0ba56059a0179698" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:c124333816c3a9b03fbeef3a9f230ba9a737e9e5bb4060aa2107a86cc0a497fc" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:63cc132e40a2e084cf01adf0775b15ac515ba905d7dcca47e9a251819c575ef3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:29401dbfa9ad77319367d36940cd8a0b3a11aba16063e39632d98b0e931ddf32" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp311-cp311-win_amd64.whl", hash = "sha256:3fc6873a41186404dad67245896a6e440baacc92f5b716ccd1bc9ed2995ab2c5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:b1d432e8d08679a40e2a6d8b2f9770a5c21793a6f9f47fdd52c5ce1948a5a8a9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:a5a1595fe639f5988ba6a8e5bc9649af3baf26df3998a0abe56c02609392e0a4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp312-cp312-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:5de54125a92bb4d1c051c0659e6fcb75256bf799a732a87184e5ea503965bce3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:fffb8ae78d8af97f849404f21411c95062db1496aeb3e56f146f0355c9989319" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:6dfcb5ee8d4d50c06a51c2fffa6cff6272098ad6540aed1a76d15fb9318194d8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:062309c1b9ea12a50e8ce661145c6aab431b1e99530d3cd60640e255778bd43a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp312-cp312-win_amd64.whl", hash = "sha256:59ef3764d0fe818125a5097d2ae867ca3fa64df032331b7e0917cf5d7bf66b13" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:f00d1345d84d8c86a63e476bb4955e46458b304b9575dcf71102b5c705320015" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:3508d914817e153ad359d7e069d752cdd736a247c322d932eb89e6bc84217f28" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp313-cp313-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:22a9d949bfc9a502d320aa04e5d02feab689d61da4e7764b62c30b991c42c5f0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f3a255b2c19987fbbe62a9dfd6cff7ff2aa9ccab3fc75218fd4b7530f01efa24" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:800250ecdadb6d9c78eae4990da62743b857b470883fa27f652db8bdde7f6659" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:6374c452ff3ec675a8f46fd9ab25c4ad0ba590b71cf0656f8b6daa5202bca3fb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp313-cp313-win_amd64.whl", hash = "sha256:61c5ad4043f791b61dd4752191d9f07f0ae412515d59ba8f005832a532f8736d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:3b71f27954685ee685317063bf13c7709a7ba74fc996b84fc6821c59b0f06468" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:38cf8125c40dae9d5acc10fa66af8ea6fdf760b2714ee482ca691fc66e6fcb18" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp313-cp313t-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:ba96630bc17c875161df3818780af30e43be9b166ce51c9a18c1feae342906c2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:1db71525a1538b30142094edb9adc10be3f3e176748cd7acc2240c2f2e5aa3a4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:15c0e1e02e93116177d29ff83e8b1619c93ddc9c49083f237d4312337a61165d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pandas/2.2.3/pandas-2.2.3-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:ad5b65698ab28ed8d7f18790a0dc58005c7629f227be9ecc1072aa74c0c1d43a" },
]

[[package]]
name = "platformdirs"
version = "4.3.7"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/platformdirs/4.3.7/platformdirs-4.3.7.tar.gz", hash = "sha256:eb437d586b6a0986388f0d6f74aa0cde27b48d0e3d66843640bfb6bdcdb6e351" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/platformdirs/4.3.7/platformdirs-4.3.7-py3-none-any.whl", hash = "sha256:a03875334331946f13c549dbd8f4bac7a13a50a895a0eb1e8c6a8ace80d40a94" },
]

[[package]]
name = "pluggy"
version = "1.5.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pluggy/1.5.0/pluggy-1.5.0.tar.gz", hash = "sha256:2cffa88e94fdc978c4c574f15f9e59b7f4201d439195c3715ca9e2486f1d0cf1" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pluggy/1.5.0/pluggy-1.5.0-py3-none-any.whl", hash = "sha256:44e1ad92c8ca002de6377e165f3e0f1be63266ab4d554740532335b9d75ea669" },
]

[[package]]
name = "polars"
version = "1.27.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/polars/1.27.1/polars-1.27.1.tar.gz", hash = "sha256:94fcb0216b56cd0594aa777db1760a41ad0dfffed90d2ca446cf9294d2e97f02" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/polars/1.27.1/polars-1.27.1-cp39-abi3-macosx_10_12_x86_64.whl", hash = "sha256:ba7ad4f8046d00dd97c1369e46a4b7e00ffcff5d38c0f847ee4b9b1bb182fb18" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/polars/1.27.1/polars-1.27.1-cp39-abi3-macosx_11_0_arm64.whl", hash = "sha256:339e3948748ad6fa7a42e613c3fb165b497ed797e93fce1aa2cddf00fbc16cac" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/polars/1.27.1/polars-1.27.1-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f801e0d9da198eb97cfb4e8af4242b8396878ff67b655c71570b7e333102b72b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/polars/1.27.1/polars-1.27.1-cp39-abi3-manylinux_2_24_aarch64.whl", hash = "sha256:4d18a29c65222451818b63cd397b2e95c20412ea0065d735a20a4a79a7b26e8a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/polars/1.27.1/polars-1.27.1-cp39-abi3-win_amd64.whl", hash = "sha256:a4f832cf478b282d97f8bf86eeae2df66fa1384de1c49bc61f7224a10cc6a5df" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/polars/1.27.1/polars-1.27.1-cp39-abi3-win_arm64.whl", hash = "sha256:4f238ee2e3c5660345cb62c0f731bbd6768362db96c058098359ecffa42c3c6c" },
]

[[package]]
name = "prism-service"
version = "0.1.0"
source = { editable = "." }
dependencies = [
    { name = "aioboto3" },
    { name = "aiokafka" },
    { name = "dacite" },
    { name = "gametime-protos" },
    { name = "jinja2" },
    { name = "mlutils", extra = ["aiohttp", "grpc", "pandas", "sklearn"] },
    { name = "polars" },
    { name = "pyarrow" },
    { name = "pyyaml" },
    { name = "redis" },
    { name = "snowflake-connector-python", extra = ["pandas"] },
    { name = "sqlglot" },
    { name = "temporalio" },
    { name = "types-aiobotocore-s3" },
]

[package.optional-dependencies]
test = [
    { name = "fakeredis" },
    { name = "grpcio" },
    { name = "kafka-python" },
    { name = "numpy" },
    { name = "pyarrow" },
    { name = "pytest" },
    { name = "pytest-asyncio" },
    { name = "testcontainers", extra = ["minio", "redis"] },
]

[package.metadata]
requires-dist = [
    { name = "aioboto3", specifier = ">=14.1.0" },
    { name = "aiokafka", specifier = ">=0.12.0" },
    { name = "dacite", specifier = ">=1.9.2" },
    { name = "fakeredis", marker = "extra == 'test'", specifier = ">=2.28.0" },
    { name = "gametime-protos", specifier = "==1.8.0", index = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" },
    { name = "grpcio", marker = "extra == 'test'", specifier = ">=1.70.0" },
    { name = "jinja2", specifier = ">=3.1.6" },
    { name = "kafka-python", marker = "extra == 'test'", specifier = ">=2.0.6" },
    { name = "mlutils", extras = ["grpc", "aiohttp", "pandas", "sklearn"], specifier = "==1.3.0", index = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" },
    { name = "numpy", marker = "extra == 'test'", specifier = ">=2.2.3" },
    { name = "polars", specifier = ">=1.27.1" },
    { name = "pyarrow", specifier = ">=19.0.1" },
    { name = "pyarrow", marker = "extra == 'test'", specifier = ">=19.0.1" },
    { name = "pytest", marker = "extra == 'test'", specifier = ">=8.3.5" },
    { name = "pytest-asyncio", marker = "extra == 'test'", specifier = ">=0.25.3" },
    { name = "pyyaml", specifier = ">=6.0.2" },
    { name = "redis", specifier = ">=5.2.1" },
    { name = "snowflake-connector-python", extras = ["pandas"], specifier = ">=3.13.2" },
    { name = "sqlglot", specifier = ">=26.9.0" },
    { name = "temporalio", specifier = ">=1.10.0" },
    { name = "testcontainers", extras = ["minio", "redis"], marker = "extra == 'test'", specifier = ">=4.9.2" },
    { name = "types-aiobotocore-s3", specifier = ">=2.21.1" },
]
provides-extras = ["test"]

[[package]]
name = "propcache"
version = "0.3.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1.tar.gz", hash = "sha256:40d980c33765359098837527e18eddefc9a24cea5b45e078a7f3bb5b032c6ecf" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-macosx_10_9_universal2.whl", hash = "sha256:7f30241577d2fef2602113b70ef7231bf4c69a97e04693bde08ddab913ba0ce5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:43593c6772aa12abc3af7784bff4a41ffa921608dd38b77cf1dfd7f5c4e71371" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:a75801768bbe65499495660b777e018cbe90c7980f07f8aa57d6be79ea6f71da" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:f6f1324db48f001c2ca26a25fa25af60711e09b9aaf4b28488602776f4f9a744" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:5cdb0f3e1eb6dfc9965d19734d8f9c481b294b5274337a8cb5cb01b462dcb7e0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:1eb34d90aac9bfbced9a58b266f8946cb5935869ff01b164573a7634d39fbcb5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f35c7070eeec2cdaac6fd3fe245226ed2a6292d3ee8c938e5bb645b434c5f256" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:b23c11c2c9e6d4e7300c92e022046ad09b91fd00e36e83c44483df4afa990073" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:3e19ea4ea0bf46179f8a3652ac1426e6dcbaf577ce4b4f65be581e237340420d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-musllinux_1_2_armv7l.whl", hash = "sha256:bd39c92e4c8f6cbf5f08257d6360123af72af9f4da75a690bef50da77362d25f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:b0313e8b923b3814d1c4a524c93dfecea5f39fa95601f6a9b1ac96cd66f89ea0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-musllinux_1_2_ppc64le.whl", hash = "sha256:e861ad82892408487be144906a368ddbe2dc6297074ade2d892341b35c59844a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-musllinux_1_2_s390x.whl", hash = "sha256:61014615c1274df8da5991a1e5da85a3ccb00c2d4701ac6f3383afd3ca47ab0a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:71ebe3fe42656a2328ab08933d420df5f3ab121772eef78f2dc63624157f0ed9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-win32.whl", hash = "sha256:58aa11f4ca8b60113d4b8e32d37e7e78bd8af4d1a5b5cb4979ed856a45e62005" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp311-cp311-win_amd64.whl", hash = "sha256:9532ea0b26a401264b1365146c440a6d78269ed41f83f23818d4b79497aeabe7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:f78eb8422acc93d7b69964012ad7048764bb45a54ba7a39bb9e146c72ea29723" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:89498dd49c2f9a026ee057965cdf8192e5ae070ce7d7a7bd4b66a8e257d0c976" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:09400e98545c998d57d10035ff623266927cb784d13dd2b31fd33b8a5316b85b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:aa8efd8c5adc5a2c9d3b952815ff8f7710cefdcaf5f2c36d26aff51aeca2f12f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:c2fe5c910f6007e716a06d269608d307b4f36e7babee5f36533722660e8c4a70" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:a0ab8cf8cdd2194f8ff979a43ab43049b1df0b37aa64ab7eca04ac14429baeb7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:563f9d8c03ad645597b8d010ef4e9eab359faeb11a0a2ac9f7b4bc8c28ebef25" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:fb6e0faf8cb6b4beea5d6ed7b5a578254c6d7df54c36ccd3d8b3eb00d6770277" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:1c5c7ab7f2bb3f573d1cb921993006ba2d39e8621019dffb1c5bc94cdbae81e8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-musllinux_1_2_armv7l.whl", hash = "sha256:050b571b2e96ec942898f8eb46ea4bfbb19bd5502424747e83badc2d4a99a44e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:e1c4d24b804b3a87e9350f79e2371a705a188d292fd310e663483af6ee6718ee" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:e4fe2a6d5ce975c117a6bb1e8ccda772d1e7029c1cca1acd209f91d30fa72815" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:feccd282de1f6322f56f6845bf1207a537227812f0a9bf5571df52bb418d79d5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:ec314cde7314d2dd0510c6787326bbffcbdc317ecee6b7401ce218b3099075a7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-win32.whl", hash = "sha256:7d2d5a0028d920738372630870e7d9644ce437142197f8c827194fca404bf03b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp312-cp312-win_amd64.whl", hash = "sha256:88c423efef9d7a59dae0614eaed718449c09a5ac79a5f224a8b9664d603f04a3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:f1528ec4374617a7a753f90f20e2f551121bb558fcb35926f99e3c42367164b8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:dc1915ec523b3b494933b5424980831b636fe483d7d543f7afb7b3bf00f0c10f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:a110205022d077da24e60b3df8bcee73971be9575dec5573dd17ae5d81751111" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:d249609e547c04d190e820d0d4c8ca03ed4582bcf8e4e160a6969ddfb57b62e5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:5ced33d827625d0a589e831126ccb4f5c29dfdf6766cac441d23995a65825dcb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:4114c4ada8f3181af20808bedb250da6bae56660e4b8dfd9cd95d4549c0962f7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:975af16f406ce48f1333ec5e912fe11064605d5c5b3f6746969077cc3adeb120" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:a34aa3a1abc50740be6ac0ab9d594e274f59960d3ad253cd318af76b996dd654" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:9cec3239c85ed15bfaded997773fdad9fb5662b0a7cbc854a43f291eb183179e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-musllinux_1_2_armv7l.whl", hash = "sha256:05543250deac8e61084234d5fc54f8ebd254e8f2b39a16b1dce48904f45b744b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:5cb5918253912e088edbf023788de539219718d3b10aef334476b62d2b53de53" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:f3bbecd2f34d0e6d3c543fdb3b15d6b60dd69970c2b4c822379e5ec8f6f621d5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:aca63103895c7d960a5b9b044a83f544b233c95e0dcff114389d64d762017af7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:5a0a9898fdb99bf11786265468571e628ba60af80dc3f6eb89a3545540c6b0ef" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-win32.whl", hash = "sha256:3a02a28095b5e63128bcae98eb59025924f121f048a62393db682f049bf4ac24" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313-win_amd64.whl", hash = "sha256:813fbb8b6aea2fc9659815e585e548fe706d6f663fa73dff59a1677d4595a037" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:a444192f20f5ce8a5e52761a031b90f5ea6288b1eef42ad4c7e64fef33540b8f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:0fbe94666e62ebe36cd652f5fc012abfbc2342de99b523f8267a678e4dfdee3c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:f011f104db880f4e2166bcdcf7f58250f7a465bc6b068dc84c824a3d4a5c94dc" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:3e584b6d388aeb0001d6d5c2bd86b26304adde6d9bb9bfa9c4889805021b96de" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:8a17583515a04358b034e241f952f1715243482fc2c2945fd99a1b03a0bd77d6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:5aed8d8308215089c0734a2af4f2e95eeb360660184ad3912686c181e500b2e7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:6d8e309ff9a0503ef70dc9a0ebd3e69cf7b3894c9ae2ae81fc10943c37762458" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:b655032b202028a582d27aeedc2e813299f82cb232f969f87a4fde491a233f11" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:9f64d91b751df77931336b5ff7bafbe8845c5770b06630e27acd5dbb71e1931c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-musllinux_1_2_armv7l.whl", hash = "sha256:19a06db789a4bd896ee91ebc50d059e23b3639c25d58eb35be3ca1cbe967c3bf" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-musllinux_1_2_i686.whl", hash = "sha256:bef100c88d8692864651b5f98e871fb090bd65c8a41a1cb0ff2322db39c96c27" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-musllinux_1_2_ppc64le.whl", hash = "sha256:87380fb1f3089d2a0b8b00f006ed12bd41bd858fabfa7330c954c70f50ed8757" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-musllinux_1_2_s390x.whl", hash = "sha256:e474fc718e73ba5ec5180358aa07f6aded0ff5f2abe700e3115c37d75c947e18" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:17d1c688a443355234f3c031349da69444be052613483f3e4158eef751abcd8a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-win32.whl", hash = "sha256:359e81a949a7619802eb601d66d37072b79b79c2505e6d3fd8b945538411400d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-cp313-cp313t-win_amd64.whl", hash = "sha256:e7fb9a84c9abbf2b2683fa3e7b0d7da4d8ecf139a1c635732a8bda29c5214b0e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/propcache/0.3.1/propcache-0.3.1-py3-none-any.whl", hash = "sha256:9a8ecf38de50a7f518c21568c80f985e776397b902f1ce0b01f799aba1608b40" },
]

[[package]]
name = "proto-plus"
version = "1.26.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "protobuf" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/proto-plus/1.26.1/proto_plus-1.26.1.tar.gz", hash = "sha256:21a515a4c4c0088a773899e23c7bbade3d18f9c66c73edd4c7ee3816bc96a012" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/proto-plus/1.26.1/proto_plus-1.26.1-py3-none-any.whl", hash = "sha256:13285478c2dcf2abb829db158e1047e2f1e8d63a077d94263c2b88b043c75a66" },
]

[[package]]
name = "protobuf"
version = "5.29.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/protobuf/5.29.1/protobuf-5.29.1.tar.gz", hash = "sha256:683be02ca21a6ffe80db6dd02c0b5b2892322c59ca57fd6c872d652cb80549cb" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/protobuf/5.29.1/protobuf-5.29.1-cp310-abi3-win32.whl", hash = "sha256:22c1f539024241ee545cbcb00ee160ad1877975690b16656ff87dde107b5f110" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/protobuf/5.29.1/protobuf-5.29.1-cp310-abi3-win_amd64.whl", hash = "sha256:1fc55267f086dd4050d18ef839d7bd69300d0d08c2a53ca7df3920cc271a3c34" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/protobuf/5.29.1/protobuf-5.29.1-cp38-abi3-macosx_10_9_universal2.whl", hash = "sha256:d473655e29c0c4bbf8b69e9a8fb54645bc289dead6d753b952e7aa660254ae18" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/protobuf/5.29.1/protobuf-5.29.1-cp38-abi3-manylinux2014_aarch64.whl", hash = "sha256:b5ba1d0e4c8a40ae0496d0e2ecfdbb82e1776928a205106d14ad6985a09ec155" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/protobuf/5.29.1/protobuf-5.29.1-cp38-abi3-manylinux2014_x86_64.whl", hash = "sha256:8ee1461b3af56145aca2800e6a3e2f928108c749ba8feccc6f5dd0062c410c0d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/protobuf/5.29.1/protobuf-5.29.1-py3-none-any.whl", hash = "sha256:32600ddb9c2a53dedc25b8581ea0f1fd8ea04956373c0c07577ce58d312522e0" },
]

[[package]]
name = "pyarrow"
version = "19.0.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1.tar.gz", hash = "sha256:3bf266b485df66a400f282ac0b6d1b500b9d2ae73314a153dbe97d6d5cc8a99e" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp311-cp311-macosx_12_0_arm64.whl", hash = "sha256:cc55d71898ea30dc95900297d191377caba257612f384207fe9f8293b5850f90" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp311-cp311-macosx_12_0_x86_64.whl", hash = "sha256:7a544ec12de66769612b2d6988c36adc96fb9767ecc8ee0a4d270b10b1c51e00" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:0148bb4fc158bfbc3d6dfe5001d93ebeed253793fff4435167f6ce1dc4bddeae" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f24faab6ed18f216a37870d8c5623f9c044566d75ec586ef884e13a02a9d62c5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp311-cp311-manylinux_2_28_aarch64.whl", hash = "sha256:4982f8e2b7afd6dae8608d70ba5bd91699077323f812a0448d8b7abdff6cb5d3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp311-cp311-manylinux_2_28_x86_64.whl", hash = "sha256:49a3aecb62c1be1d822f8bf629226d4a96418228a42f5b40835c1f10d42e4db6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp311-cp311-win_amd64.whl", hash = "sha256:008a4009efdb4ea3d2e18f05cd31f9d43c388aad29c636112c2966605ba33466" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp312-cp312-macosx_12_0_arm64.whl", hash = "sha256:80b2ad2b193e7d19e81008a96e313fbd53157945c7be9ac65f44f8937a55427b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp312-cp312-macosx_12_0_x86_64.whl", hash = "sha256:ee8dec072569f43835932a3b10c55973593abc00936c202707a4ad06af7cb294" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:4d5d1ec7ec5324b98887bdc006f4d2ce534e10e60f7ad995e7875ffa0ff9cb14" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f3ad4c0eb4e2a9aeb990af6c09e6fa0b195c8c0e7b272ecc8d4d2b6574809d34" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp312-cp312-manylinux_2_28_aarch64.whl", hash = "sha256:d383591f3dcbe545f6cc62daaef9c7cdfe0dff0fb9e1c8121101cabe9098cfa6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp312-cp312-manylinux_2_28_x86_64.whl", hash = "sha256:b4c4156a625f1e35d6c0b2132635a237708944eb41df5fbe7d50f20d20c17832" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp312-cp312-win_amd64.whl", hash = "sha256:5bd1618ae5e5476b7654c7b55a6364ae87686d4724538c24185bbb2952679960" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp313-cp313-macosx_12_0_arm64.whl", hash = "sha256:e45274b20e524ae5c39d7fc1ca2aa923aab494776d2d4b316b49ec7572ca324c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp313-cp313-macosx_12_0_x86_64.whl", hash = "sha256:d9dedeaf19097a143ed6da37f04f4051aba353c95ef507764d344229b2b740ae" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:6ebfb5171bb5f4a52319344ebbbecc731af3f021e49318c74f33d520d31ae0c4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f2a21d39fbdb948857f67eacb5bbaaf36802de044ec36fbef7a1c8f0dd3a4ab2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp313-cp313-manylinux_2_28_aarch64.whl", hash = "sha256:99bc1bec6d234359743b01e70d4310d0ab240c3d6b0da7e2a93663b0158616f6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp313-cp313-manylinux_2_28_x86_64.whl", hash = "sha256:1b93ef2c93e77c442c979b0d596af45e4665d8b96da598db145b0fec014b9136" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp313-cp313-win_amd64.whl", hash = "sha256:d9d46e06846a41ba906ab25302cf0fd522f81aa2a85a71021826f34639ad31ef" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp313-cp313t-macosx_12_0_arm64.whl", hash = "sha256:c0fe3dbbf054a00d1f162fda94ce236a899ca01123a798c561ba307ca38af5f0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp313-cp313t-macosx_12_0_x86_64.whl", hash = "sha256:96606c3ba57944d128e8a8399da4812f56c7f61de8c647e3470b417f795d0ef9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:8f04d49a6b64cf24719c080b3c2029a3a5b16417fd5fd7c4041f94233af732f3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:5a9137cf7e1640dce4c190551ee69d478f7121b5c6f323553b319cac936395f6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp313-cp313t-manylinux_2_28_aarch64.whl", hash = "sha256:7c1bca1897c28013db5e4c83944a2ab53231f541b9e0c3f4791206d0c0de389a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyarrow/19.0.1/pyarrow-19.0.1-cp313-cp313t-manylinux_2_28_x86_64.whl", hash = "sha256:58d9397b2e273ef76264b45531e9d552d8ec8a6688b7390b5be44c02a37aade8" },
]

[[package]]
name = "pyasn1"
version = "0.6.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyasn1/0.6.1/pyasn1-0.6.1.tar.gz", hash = "sha256:6f580d2bdd84365380830acf45550f2511469f673cb4a5ae3857a3170128b034" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyasn1/0.6.1/pyasn1-0.6.1-py3-none-any.whl", hash = "sha256:0d632f46f2ba09143da3a8afe9e33fb6f92fa2320ab7e886e2d0f7672af84629" },
]

[[package]]
name = "pyasn1-modules"
version = "0.4.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "pyasn1" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyasn1-modules/0.4.2/pyasn1_modules-0.4.2.tar.gz", hash = "sha256:677091de870a80aae844b1ca6134f54652fa2c8c5a52aa396440ac3106e941e6" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyasn1-modules/0.4.2/pyasn1_modules-0.4.2-py3-none-any.whl", hash = "sha256:29253a9207ce32b64c3ac6600edc75368f98473906e8fd1043bd6b5b1de2c14a" },
]

[[package]]
name = "pycparser"
version = "2.22"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pycparser/2.22/pycparser-2.22.tar.gz", hash = "sha256:491c8be9c040f5390f5bf44a5b07752bd07f56edf992381b05c701439eec10f6" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pycparser/2.22/pycparser-2.22-py3-none-any.whl", hash = "sha256:c3702b6d3dd8c7abc1afa565d7e63d53a1d0bd86cdc24edd75470f4de499cfcc" },
]

[[package]]
name = "pycryptodome"
version = "3.22.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pycryptodome/3.22.0/pycryptodome-3.22.0.tar.gz", hash = "sha256:fd7ab568b3ad7b77c908d7c3f7e167ec5a8f035c64ff74f10d47a4edd043d723" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pycryptodome/3.22.0/pycryptodome-3.22.0-cp37-abi3-macosx_10_9_universal2.whl", hash = "sha256:009e1c80eea42401a5bd5983c4bab8d516aef22e014a4705622e24e6d9d703c6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pycryptodome/3.22.0/pycryptodome-3.22.0-cp37-abi3-macosx_10_9_x86_64.whl", hash = "sha256:3b76fa80daeff9519d7e9f6d9e40708f2fce36b9295a847f00624a08293f4f00" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pycryptodome/3.22.0/pycryptodome-3.22.0-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:a31fa5914b255ab62aac9265654292ce0404f6b66540a065f538466474baedbc" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pycryptodome/3.22.0/pycryptodome-3.22.0-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:a0092fd476701eeeb04df5cc509d8b739fa381583cda6a46ff0a60639b7cd70d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pycryptodome/3.22.0/pycryptodome-3.22.0-cp37-abi3-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:18d5b0ddc7cf69231736d778bd3ae2b3efb681ae33b64b0c92fb4626bb48bb89" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pycryptodome/3.22.0/pycryptodome-3.22.0-cp37-abi3-musllinux_1_2_aarch64.whl", hash = "sha256:f6cf6aa36fcf463e622d2165a5ad9963b2762bebae2f632d719dfb8544903cf5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pycryptodome/3.22.0/pycryptodome-3.22.0-cp37-abi3-musllinux_1_2_i686.whl", hash = "sha256:aec7b40a7ea5af7c40f8837adf20a137d5e11a6eb202cde7e588a48fb2d871a8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pycryptodome/3.22.0/pycryptodome-3.22.0-cp37-abi3-musllinux_1_2_x86_64.whl", hash = "sha256:d21c1eda2f42211f18a25db4eaf8056c94a8563cd39da3683f89fe0d881fb772" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pycryptodome/3.22.0/pycryptodome-3.22.0-cp37-abi3-win32.whl", hash = "sha256:f02baa9f5e35934c6e8dcec91fcde96612bdefef6e442813b8ea34e82c84bbfb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pycryptodome/3.22.0/pycryptodome-3.22.0-cp37-abi3-win_amd64.whl", hash = "sha256:d086aed307e96d40c23c42418cbbca22ecc0ab4a8a0e24f87932eeab26c08627" },
]

[[package]]
name = "pyjwt"
version = "2.10.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyjwt/2.10.1/pyjwt-2.10.1.tar.gz", hash = "sha256:3cc5772eb20009233caf06e9d8a0577824723b44e6648ee0a2aedb6cf9381953" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyjwt/2.10.1/PyJWT-2.10.1-py3-none-any.whl", hash = "sha256:dcdd193e30abefd5debf142f9adfcdd2b58004e644f25406ffaebd50bd98dacb" },
]

[[package]]
name = "pyopenssl"
version = "24.3.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "cryptography" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyopenssl/24.3.0/pyopenssl-24.3.0.tar.gz", hash = "sha256:49f7a019577d834746bc55c5fce6ecbcec0f2b4ec5ce1cf43a9a173b8138bb36" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyopenssl/24.3.0/pyOpenSSL-24.3.0-py3-none-any.whl", hash = "sha256:e474f5a473cd7f92221cc04976e48f4d11502804657a08a989fb3be5514c904a" },
]

[[package]]
name = "pytest"
version = "8.3.5"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "iniconfig" },
    { name = "packaging" },
    { name = "pluggy" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pytest/8.3.5/pytest-8.3.5.tar.gz", hash = "sha256:f4efe70cc14e511565ac476b57c279e12a855b11f48f212af1080ef2263d3845" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pytest/8.3.5/pytest-8.3.5-py3-none-any.whl", hash = "sha256:c69214aa47deac29fad6c2a4f590b9c4a9fdb16a403176fe154b79c0b4d4d820" },
]

[[package]]
name = "pytest-asyncio"
version = "0.26.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "pytest" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pytest-asyncio/0.26.0/pytest_asyncio-0.26.0.tar.gz", hash = "sha256:c4df2a697648241ff39e7f0e4a73050b03f123f760673956cf0d72a4990e312f" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pytest-asyncio/0.26.0/pytest_asyncio-0.26.0-py3-none-any.whl", hash = "sha256:7b51ed894f4fbea1340262bdae5135797ebbe21d8638978e35d31c6d19f72fb0" },
]

[[package]]
name = "python-dateutil"
version = "2.9.0.post0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "six" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/python-dateutil/2.9.0.post0/python-dateutil-2.9.0.post0.tar.gz", hash = "sha256:37dd54208da7e1cd875388217d5e00ebd4179249f90fb72437e91a35459a0ad3" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/python-dateutil/2.9.0.post0/python_dateutil-2.9.0.post0-py2.py3-none-any.whl", hash = "sha256:a8b2bc7bffae282281c8140a97d3aa9c14da0b136dfe83f850eea9a5f7470427" },
]

[[package]]
name = "python-dotenv"
version = "1.1.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/python-dotenv/1.1.0/python_dotenv-1.1.0.tar.gz", hash = "sha256:41f90bc6f5f177fb41f53e87666db362025010eb28f60a01c9143bfa33a2b2d5" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/python-dotenv/1.1.0/python_dotenv-1.1.0-py3-none-any.whl", hash = "sha256:d7c01d9e2293916c18baf562d95698754b0dbbb5e74d457c45d4f6561fb9d55d" },
]

[[package]]
name = "pytz"
version = "2025.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pytz/2025.2/pytz-2025.2.tar.gz", hash = "sha256:360b9e3dbb49a209c21ad61809c7fb453643e048b38924c765813546746e81c3" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pytz/2025.2/pytz-2025.2-py2.py3-none-any.whl", hash = "sha256:5ddf76296dd8c44c26eb8f4b6f35488f3ccbf6fbbd7adee0b7262d43f0ec2f00" },
]

[[package]]
name = "pywin32"
version = "310"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pywin32/310/pywin32-310-cp311-cp311-win32.whl", hash = "sha256:1e765f9564e83011a63321bb9d27ec456a0ed90d3732c4b2e312b855365ed8bd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pywin32/310/pywin32-310-cp311-cp311-win_amd64.whl", hash = "sha256:126298077a9d7c95c53823934f000599f66ec9296b09167810eb24875f32689c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pywin32/310/pywin32-310-cp311-cp311-win_arm64.whl", hash = "sha256:19ec5fc9b1d51c4350be7bb00760ffce46e6c95eaf2f0b2f1150657b1a43c582" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pywin32/310/pywin32-310-cp312-cp312-win32.whl", hash = "sha256:8a75a5cc3893e83a108c05d82198880704c44bbaee4d06e442e471d3c9ea4f3d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pywin32/310/pywin32-310-cp312-cp312-win_amd64.whl", hash = "sha256:bf5c397c9a9a19a6f62f3fb821fbf36cac08f03770056711f765ec1503972060" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pywin32/310/pywin32-310-cp312-cp312-win_arm64.whl", hash = "sha256:2349cc906eae872d0663d4d6290d13b90621eaf78964bb1578632ff20e152966" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pywin32/310/pywin32-310-cp313-cp313-win32.whl", hash = "sha256:5d241a659c496ada3253cd01cfaa779b048e90ce4b2b38cd44168ad555ce74ab" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pywin32/310/pywin32-310-cp313-cp313-win_amd64.whl", hash = "sha256:667827eb3a90208ddbdcc9e860c81bde63a135710e21e4cb3348968e4bd5249e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pywin32/310/pywin32-310-cp313-cp313-win_arm64.whl", hash = "sha256:e308f831de771482b7cf692a1f308f8fca701b2d8f9dde6cc440c7da17e47b33" },
]

[[package]]
name = "pyyaml"
version = "6.0.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/pyyaml-6.0.2.tar.gz", hash = "sha256:d584d9ec91ad65861cc08d42e834324ef890a082e591037abe114850ff7bbc3e" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:cc1c1159b3d456576af7a3e4d1ba7e6924cb39de8f67111c735f6fc832082774" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:1e2120ef853f59c7419231f3bf4e7021f1b936f6ebd222406c3b60212205d2ee" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:5d225db5a45f21e78dd9358e58a98702a0302f2659a3c6cd320564b75b86f47c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:5ac9328ec4831237bec75defaf839f7d4564be1e6b25ac710bd1a96321cc8317" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:3ad2a3decf9aaba3d29c8f537ac4b243e36bef957511b4766cb0057d32b0be85" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-musllinux_1_1_aarch64.whl", hash = "sha256:ff3824dc5261f50c9b0dfb3be22b4567a6f938ccce4587b38952d85fd9e9afe4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-musllinux_1_1_x86_64.whl", hash = "sha256:797b4f722ffa07cc8d62053e4cff1486fa6dc094105d13fea7b1de7d8bf71c9e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-win32.whl", hash = "sha256:11d8f3dd2b9c1207dcaf2ee0bbbfd5991f571186ec9cc78427ba5bd32afae4b5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-win_amd64.whl", hash = "sha256:e10ce637b18caea04431ce14fabcf5c64a1c61ec9c56b071a4b7ca131ca52d44" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:c70c95198c015b85feafc136515252a261a84561b7b1d51e3384e0655ddf25ab" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:ce826d6ef20b1bc864f0a68340c8b3287705cae2f8b4b1d932177dcc76721725" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1f71ea527786de97d1a0cc0eacd1defc0985dcf6b3f17bb77dcfc8c34bec4dc5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:9b22676e8097e9e22e36d6b7bda33190d0d400f345f23d4065d48f4ca7ae0425" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:80bab7bfc629882493af4aa31a4cfa43a4c57c83813253626916b8c7ada83476" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:0833f8694549e586547b576dcfaba4a6b55b9e96098b36cdc7ebefe667dfed48" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:8b9c7197f7cb2738065c481a0461e50ad02f18c78cd75775628afb4d7137fb3b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-win32.whl", hash = "sha256:ef6107725bd54b262d6dedcc2af448a266975032bc85ef0172c5f059da6325b4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-win_amd64.whl", hash = "sha256:7e7401d0de89a9a855c839bc697c079a4af81cf878373abd7dc625847d25cbd8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:efdca5630322a10774e8e98e1af481aad470dd62c3170801852d752aa7a783ba" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:50187695423ffe49e2deacb8cd10510bc361faac997de9efef88badc3bb9e2d1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:0ffe8360bab4910ef1b9e87fb812d8bc0a308b0d0eef8c8f44e0254ab3b07133" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:17e311b6c678207928d649faa7cb0d7b4c26a0ba73d41e99c4fff6b6c3276484" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:70b189594dbe54f75ab3a1acec5f1e3faa7e8cf2f1e08d9b561cb41b845f69d5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-musllinux_1_1_aarch64.whl", hash = "sha256:41e4e3953a79407c794916fa277a82531dd93aad34e29c2a514c2c0c5fe971cc" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-musllinux_1_1_x86_64.whl", hash = "sha256:68ccc6023a3400877818152ad9a1033e3db8625d899c72eacb5a668902e4d652" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-win32.whl", hash = "sha256:bc2fa7c6b47d6bc618dd7fb02ef6fdedb1090ec036abab80d4681424b84c1183" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-win_amd64.whl", hash = "sha256:8388ee1976c416731879ac16da0aff3f63b286ffdd57cdeb95f3f2e085687563" },
]

[[package]]
name = "redis"
version = "5.2.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "async-timeout", marker = "python_full_version < '3.11.3'" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/redis/5.2.1/redis-5.2.1.tar.gz", hash = "sha256:16f2e22dff21d5125e8481515e386711a34cbec50f0e44413dd7d9c060a54e0f" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/redis/5.2.1/redis-5.2.1-py3-none-any.whl", hash = "sha256:ee7e1056b9aea0f04c6c2ed59452947f34c4940ee025f5dd83e6a6418b6989e4" },
]

[[package]]
name = "requests"
version = "2.32.3"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "certifi" },
    { name = "charset-normalizer" },
    { name = "idna" },
    { name = "urllib3" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/requests/2.32.3/requests-2.32.3.tar.gz", hash = "sha256:55365417734eb18255590a9ff9eb97e9e1da868d4ccd6402399eaf68af20a760" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/requests/2.32.3/requests-2.32.3-py3-none-any.whl", hash = "sha256:70761cfe03c773ceb22aa2f671b4757976145175cdfca038c02654d061d6dcc6" },
]

[[package]]
name = "rsa"
version = "4.9"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "pyasn1" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/rsa/4.9/rsa-4.9.tar.gz", hash = "sha256:e38464a49c6c85d7f1351b0126661487a7e0a14a50f1675ec50eb34d4f20ef21" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/rsa/4.9/rsa-4.9-py3-none-any.whl", hash = "sha256:90260d9058e514786967344d0ef75fa8727eed8a7d2e43ce9f4bcf1b536174f7" },
]

[[package]]
name = "s3transfer"
version = "0.11.3"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "botocore" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/s3transfer/0.11.3/s3transfer-0.11.3.tar.gz", hash = "sha256:edae4977e3a122445660c7c114bba949f9d191bae3b34a096f18a1c8c354527a" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/s3transfer/0.11.3/s3transfer-0.11.3-py3-none-any.whl", hash = "sha256:ca855bdeb885174b5ffa95b9913622459d4ad8e331fc98eb01e6d5eb6a30655d" },
]

[[package]]
name = "scikit-learn"
version = "1.6.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "joblib" },
    { name = "numpy" },
    { name = "scipy" },
    { name = "threadpoolctl" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1.tar.gz", hash = "sha256:b4fc2525eca2c69a59260f583c56a7557c6ccdf8deafdba6e060f94c1c59738e" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:72abc587c75234935e97d09aa4913a82f7b03ee0b74111dcc2881cba3c5a7b33" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp311-cp311-macosx_12_0_arm64.whl", hash = "sha256:b3b00cdc8f1317b5f33191df1386c0befd16625f49d979fe77a8d44cae82410d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:dc4765af3386811c3ca21638f63b9cf5ecf66261cc4815c1db3f1e7dc7b79db2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:25fc636bdaf1cc2f4a124a116312d837148b5e10872147bdaf4887926b8c03d8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp311-cp311-win_amd64.whl", hash = "sha256:fa909b1a36e000a03c382aade0bd2063fd5680ff8b8e501660c0f59f021a6415" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:926f207c804104677af4857b2c609940b743d04c4c35ce0ddc8ff4f053cddc1b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp312-cp312-macosx_12_0_arm64.whl", hash = "sha256:2c2cae262064e6a9b77eee1c8e768fc46aa0b8338c6a8297b9b6759720ec0ff2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1061b7c028a8663fb9a1a1baf9317b64a257fcb036dae5c8752b2abef31d136f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:2e69fab4ebfc9c9b580a7a80111b43d214ab06250f8a7ef590a4edf72464dd86" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp312-cp312-win_amd64.whl", hash = "sha256:70b1d7e85b1c96383f872a519b3375f92f14731e279a7b4c6cfd650cf5dffc52" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:2ffa1e9e25b3d93990e74a4be2c2fc61ee5af85811562f1288d5d055880c4322" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp313-cp313-macosx_12_0_arm64.whl", hash = "sha256:dc5cf3d68c5a20ad6d571584c0750ec641cc46aeef1c1507be51300e6003a7e1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:c06beb2e839ecc641366000ca84f3cf6fa9faa1777e29cf0c04be6e4d096a348" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:e8ca8cb270fee8f1f76fa9bfd5c3507d60c6438bbee5687f81042e2bb98e5a97" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp313-cp313-win_amd64.whl", hash = "sha256:7a1c43c8ec9fde528d664d947dc4c0789be4077a3647f232869f41d9bf50e0fb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:a17c1dea1d56dcda2fac315712f3651a1fea86565b64b48fa1bc090249cbf236" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp313-cp313t-macosx_12_0_arm64.whl", hash = "sha256:6a7aa5f9908f0f28f4edaa6963c0a6183f1911e63a69aa03782f0d924c830a35" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:0650e730afb87402baa88afbf31c07b84c98272622aaba002559b614600ca691" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scikit-learn/1.6.1/scikit_learn-1.6.1-cp313-cp313t-win_amd64.whl", hash = "sha256:3f59fe08dc03ea158605170eb52b22a105f238a5d512c4470ddeca71feae8e5f" },
]

[[package]]
name = "scipy"
version = "1.15.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "numpy" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2.tar.gz", hash = "sha256:cd58a314d92838f7e6f755c8a2167ead4f27e1fd5c1251fd54289569ef3495ec" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp311-cp311-macosx_10_13_x86_64.whl", hash = "sha256:92233b2df6938147be6fa8824b8136f29a18f016ecde986666be5f4d686a91a4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp311-cp311-macosx_12_0_arm64.whl", hash = "sha256:62ca1ff3eb513e09ed17a5736929429189adf16d2d740f44e53270cc800ecff1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp311-cp311-macosx_14_0_arm64.whl", hash = "sha256:4c6676490ad76d1c2894d77f976144b41bd1a4052107902238047fb6a473e971" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp311-cp311-macosx_14_0_x86_64.whl", hash = "sha256:a8bf5cb4a25046ac61d38f8d3c3426ec11ebc350246a4642f2f315fe95bda655" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:6a8e34cf4c188b6dd004654f88586d78f95639e48a25dfae9c5e34a6dc34547e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:28a0d2c2075946346e4408b211240764759e0fabaeb08d871639b5f3b1aca8a0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:42dabaaa798e987c425ed76062794e93a243be8f0f20fff6e7a89f4d61cb3d40" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:6f5e296ec63c5da6ba6fa0343ea73fd51b8b3e1a300b0a8cae3ed4b1122c7462" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp311-cp311-win_amd64.whl", hash = "sha256:597a0c7008b21c035831c39927406c6181bcf8f60a73f36219b69d010aa04737" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:c4697a10da8f8765bb7c83e24a470da5797e37041edfd77fd95ba3811a47c4fd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp312-cp312-macosx_12_0_arm64.whl", hash = "sha256:869269b767d5ee7ea6991ed7e22b3ca1f22de73ab9a49c44bad338b725603301" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp312-cp312-macosx_14_0_arm64.whl", hash = "sha256:bad78d580270a4d32470563ea86c6590b465cb98f83d760ff5b0990cb5518a93" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp312-cp312-macosx_14_0_x86_64.whl", hash = "sha256:b09ae80010f52efddb15551025f9016c910296cf70adbf03ce2a8704f3a5ad20" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:5a6fd6eac1ce74a9f77a7fc724080d507c5812d61e72bd5e4c489b042455865e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:2b871df1fe1a3ba85d90e22742b93584f8d2b8e6124f8372ab15c71b73e428b8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:03205d57a28e18dfd39f0377d5002725bf1f19a46f444108c29bdb246b6c8a11" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:601881dfb761311045b03114c5fe718a12634e5608c3b403737ae463c9885d53" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp312-cp312-win_amd64.whl", hash = "sha256:e7c68b6a43259ba0aab737237876e5c2c549a031ddb7abc28c7b47f22e202ded" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:01edfac9f0798ad6b46d9c4c9ca0e0ad23dbf0b1eb70e96adb9fa7f525eff0bf" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313-macosx_12_0_arm64.whl", hash = "sha256:08b57a9336b8e79b305a143c3655cc5bdbe6d5ece3378578888d2afbb51c4e37" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313-macosx_14_0_arm64.whl", hash = "sha256:54c462098484e7466362a9f1672d20888f724911a74c22ae35b61f9c5919183d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313-macosx_14_0_x86_64.whl", hash = "sha256:cf72ff559a53a6a6d77bd8eefd12a17995ffa44ad86c77a5df96f533d4e6c6bb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:9de9d1416b3d9e7df9923ab23cd2fe714244af10b763975bea9e4f2e81cebd27" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:fb530e4794fc8ea76a4a21ccb67dea33e5e0e60f07fc38a49e821e1eae3b71a0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:5ea7ed46d437fc52350b028b1d44e002646e28f3e8ddc714011aaf87330f2f32" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:11e7ad32cf184b74380f43d3c0a706f49358b904fa7d5345f16ddf993609184d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313-win_amd64.whl", hash = "sha256:a5080a79dfb9b78b768cebf3c9dcbc7b665c5875793569f48bf0e2b1d7f68f6f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:447ce30cee6a9d5d1379087c9e474628dab3db4a67484be1b7dc3196bfb2fac9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313t-macosx_12_0_arm64.whl", hash = "sha256:c90ebe8aaa4397eaefa8455a8182b164a6cc1d59ad53f79943f266d99f68687f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313t-macosx_14_0_arm64.whl", hash = "sha256:def751dd08243934c884a3221156d63e15234a3155cf25978b0a668409d45eb6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313t-macosx_14_0_x86_64.whl", hash = "sha256:302093e7dfb120e55515936cb55618ee0b895f8bcaf18ff81eca086c17bd80af" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:7cd5b77413e1855351cdde594eca99c1f4a588c2d63711388b6a1f1c01f62274" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:6d0194c37037707b2afa7a2f2a924cf7bac3dc292d51b6a925e5fcb89bc5c776" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:bae43364d600fdc3ac327db99659dcb79e6e7ecd279a75fe1266669d9a652828" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:f031846580d9acccd0044efd1a90e6f4df3a6e12b4b6bd694a7bc03a89892b28" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/scipy/1.15.2/scipy-1.15.2-cp313-cp313t-win_amd64.whl", hash = "sha256:fe8a9eb875d430d81755472c5ba75e84acc980e4a8f6204d402849234d3017db" },
]

[[package]]
name = "six"
version = "1.17.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/six/1.17.0/six-1.17.0.tar.gz", hash = "sha256:ff70335d468e7eb6ec65b95b99d3a2836546063f63acc5171de367e834932a81" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/six/1.17.0/six-1.17.0-py2.py3-none-any.whl", hash = "sha256:4721f391ed90541fddacab5acf947aa0d3dc7d27b2e1e8eda2be8970586c3274" },
]

[[package]]
name = "snowflake-connector-python"
version = "3.13.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "asn1crypto" },
    { name = "certifi" },
    { name = "cffi" },
    { name = "charset-normalizer" },
    { name = "cryptography" },
    { name = "filelock" },
    { name = "idna" },
    { name = "packaging" },
    { name = "platformdirs" },
    { name = "pyjwt" },
    { name = "pyopenssl" },
    { name = "pytz" },
    { name = "requests" },
    { name = "sortedcontainers" },
    { name = "tomlkit" },
    { name = "typing-extensions" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/snowflake-connector-python/3.13.2/snowflake_connector_python-3.13.2.tar.gz", hash = "sha256:c9954a5e237566420e087a4fcef227775e0c62fbfc821e0ef6f81325fd44c904" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/snowflake-connector-python/3.13.2/snowflake_connector_python-3.13.2-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:fd693b1db31c70a9669b5818980f718149d6f7b4624628bed087801dcd617051" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/snowflake-connector-python/3.13.2/snowflake_connector_python-3.13.2-cp311-cp311-macosx_11_0_x86_64.whl", hash = "sha256:67fcde6666075cc8e6e2fd4ba9dbf1291af780567ffe55a5adbb808de715b39f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/snowflake-connector-python/3.13.2/snowflake_connector_python-3.13.2-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:8092ec250b1dcc7c38d8a101a29e9118be56079d8e4f410a50159421c22b3b8e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/snowflake-connector-python/3.13.2/snowflake_connector_python-3.13.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:8912334af6851d325a5f2bc72416e6a46be889d045e0e09412084b99602c3122" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/snowflake-connector-python/3.13.2/snowflake_connector_python-3.13.2-cp311-cp311-win_amd64.whl", hash = "sha256:c11599b5d19b4aaab880b5e7b57525645dc1ee9768acc7dad11abf6998c75b22" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/snowflake-connector-python/3.13.2/snowflake_connector_python-3.13.2-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:1cf34116687653b7467d519da1dfdbff4f58d0032932d31d61f9d27717e7d61f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/snowflake-connector-python/3.13.2/snowflake_connector_python-3.13.2-cp312-cp312-macosx_11_0_x86_64.whl", hash = "sha256:38426293ecf6c1fc3dd87d506e55d8b82dcf763fab1c827b0d09bc74f9852c50" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/snowflake-connector-python/3.13.2/snowflake_connector_python-3.13.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:fd95811b8378d8a268038b59ea8dba8d30dd0b96a1c323191805ae152224ff70" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/snowflake-connector-python/3.13.2/snowflake_connector_python-3.13.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:1fe0cd6fab07fccdea020394a6175baa1ddf57b3d1f4dc288bd7eebcf29c6a0b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/snowflake-connector-python/3.13.2/snowflake_connector_python-3.13.2-cp312-cp312-win_amd64.whl", hash = "sha256:82028c55d949889f759b78dd86b6249e9e4934cd6fcbe026cf7f41aefc9eb999" },
]

[package.optional-dependencies]
pandas = [
    { name = "pandas" },
    { name = "pyarrow" },
]

[[package]]
name = "sortedcontainers"
version = "2.4.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/sortedcontainers/2.4.0/sortedcontainers-2.4.0.tar.gz", hash = "sha256:25caa5a06cc30b6b83d11423433f65d1f9d76c4c6a0c90e3379eaa43b9bfdb88" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/sortedcontainers/2.4.0/sortedcontainers-2.4.0-py2.py3-none-any.whl", hash = "sha256:a163dcaede0f1c021485e957a39245190e74249897e2ae4b2aa38595db237ee0" },
]

[[package]]
name = "sqlglot"
version = "26.12.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/sqlglot/26.12.1/sqlglot-26.12.1.tar.gz", hash = "sha256:ee67df004208650056d81cd2ed70fc7cc9c4a0711049b596db4a47e949c9a0bf" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/sqlglot/26.12.1/sqlglot-26.12.1-py3-none-any.whl", hash = "sha256:46c3b55c914310240f576ccb5ee652df36f18ec1553ac7174f409e291224cf55" },
]

[[package]]
name = "temporalio"
version = "1.10.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "protobuf" },
    { name = "types-protobuf" },
    { name = "typing-extensions" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/temporalio/1.10.0/temporalio-1.10.0.tar.gz", hash = "sha256:688400e4ca7f6b47c0ade3ebb6549e4d79b0762430ea735a0d8ff83b1ab8b8ba" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/temporalio/1.10.0/temporalio-1.10.0-cp39-abi3-macosx_10_9_x86_64.whl", hash = "sha256:81fd40eeeba0396a7193ab5b45877301234b983aa38e444dfceecad2b3224398" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/temporalio/1.10.0/temporalio-1.10.0-cp39-abi3-macosx_11_0_arm64.whl", hash = "sha256:2f7bff9ac1fc832655342e9677bbee1b413b97857d1f2265f018ce72fb7758f8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/temporalio/1.10.0/temporalio-1.10.0-cp39-abi3-manylinux2014_aarch64.manylinux_2_17_aarch64.whl", hash = "sha256:c3ee8416d1cab04036e03e39a4db4cf4a9a799750b8da20022e0d719da9c9371" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/temporalio/1.10.0/temporalio-1.10.0-cp39-abi3-manylinux2014_x86_64.manylinux_2_17_x86_64.whl", hash = "sha256:4f5805e0d33ba525ccea01e3cd9d3eca313e0c66c6b86e6cb0f15ef004c0acc0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/temporalio/1.10.0/temporalio-1.10.0-cp39-abi3-win_amd64.whl", hash = "sha256:81cb8bef8aef6d3cc130c7cecf008cf529177a2f9cb206cfba2897db7df9d093" },
]

[[package]]
name = "testcontainers"
version = "4.9.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "docker" },
    { name = "python-dotenv" },
    { name = "typing-extensions" },
    { name = "urllib3" },
    { name = "wrapt" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/testcontainers/4.9.2/testcontainers-4.9.2.tar.gz", hash = "sha256:348c72d369d0bd52b57ab4f07a965ae9562837098c28f0522b969b064b779f10" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/testcontainers/4.9.2/testcontainers-4.9.2-py3-none-any.whl", hash = "sha256:36bd2b58d91f2fc7ac4f4a73c6ec00e5e60c259c10f208dbfe3161029889be92" },
]

[package.optional-dependencies]
minio = [
    { name = "minio" },
]
redis = [
    { name = "redis" },
]

[[package]]
name = "threadpoolctl"
version = "3.6.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/threadpoolctl/3.6.0/threadpoolctl-3.6.0.tar.gz", hash = "sha256:8ab8b4aa3491d812b623328249fab5302a68d2d71745c8a4c719a2fcaba9f44e" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/threadpoolctl/3.6.0/threadpoolctl-3.6.0-py3-none-any.whl", hash = "sha256:43a0b8fd5a2928500110039e43a5eed8480b918967083ea48dc3ab9f13c4a7fb" },
]

[[package]]
name = "tomlkit"
version = "0.13.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomlkit/0.13.2/tomlkit-0.13.2.tar.gz", hash = "sha256:fff5fe59a87295b278abd31bec92c15d9bc4a06885ab12bcea52c71119392e79" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomlkit/0.13.2/tomlkit-0.13.2-py3-none-any.whl", hash = "sha256:7a974427f6e119197f670fbbbeae7bef749a6c14e793db934baefc1b5f03efde" },
]

[[package]]
name = "types-aiobotocore-s3"
version = "2.21.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "typing-extensions", marker = "python_full_version < '3.12'" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/types-aiobotocore-s3/2.21.1/types_aiobotocore_s3-2.21.1.tar.gz", hash = "sha256:5c8a68b78d577006062de4c158bd902eab2c38e81570be5bac0227375f9d8153" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/types-aiobotocore-s3/2.21.1/types_aiobotocore_s3-2.21.1-py3-none-any.whl", hash = "sha256:64b04b7729a3322e4758401e722c21277aa0e798c00d0077c4f43a9f584858fd" },
]

[[package]]
name = "types-protobuf"
version = "5.29.1.20250315"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/types-protobuf/5.29.1.20250315/types_protobuf-5.29.1.20250315.tar.gz", hash = "sha256:0b05bc34621d046de54b94fddd5f4eb3bf849fe2e13a50f8fb8e89f35045ff49" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/types-protobuf/5.29.1.20250315/types_protobuf-5.29.1.20250315-py3-none-any.whl", hash = "sha256:57efd51fd0979d1f5e1d94053d1e7cfff9c028e8d05b17e341b91a1c7fce37c4" },
]

[[package]]
name = "typing-extensions"
version = "4.13.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/typing-extensions/4.13.0/typing_extensions-4.13.0.tar.gz", hash = "sha256:0a4ac55a5820789d87e297727d229866c9650f6521b64206413c4fbada24d95b" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/typing-extensions/4.13.0/typing_extensions-4.13.0-py3-none-any.whl", hash = "sha256:c8dd92cc0d6425a97c18fbb9d1954e5ff92c1ca881a309c45f06ebc0b79058e5" },
]

[[package]]
name = "tzdata"
version = "2025.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tzdata/2025.2/tzdata-2025.2.tar.gz", hash = "sha256:b60a638fcc0daffadf82fe0f57e53d06bdec2f36c4df66280ae79bce6bd6f2b9" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tzdata/2025.2/tzdata-2025.2-py2.py3-none-any.whl", hash = "sha256:1a403fada01ff9221ca8044d701868fa132215d84beb92242d9acd2147f667a8" },
]

[[package]]
name = "urllib3"
version = "2.3.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/urllib3/2.3.0/urllib3-2.3.0.tar.gz", hash = "sha256:f8c5449b3cf0861679ce7e0503c7b44b5ec981bec0d1d3795a07f1ba96f0204d" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/urllib3/2.3.0/urllib3-2.3.0-py3-none-any.whl", hash = "sha256:1cee9ad369867bfdbbb48b7dd50374c0967a0bb7710050facf0dd6911440e3df" },
]

[[package]]
name = "wrapt"
version = "1.17.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2.tar.gz", hash = "sha256:41388e9d4d1522446fe79d3213196bd9e3b301a336965b9e27ca2788ebd122f3" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp311-cp311-macosx_10_9_universal2.whl", hash = "sha256:ff04ef6eec3eee8a5efef2401495967a916feaa353643defcc03fc74fe213b58" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:4db983e7bca53819efdbd64590ee96c9213894272c776966ca6306b73e4affda" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:9abc77a4ce4c6f2a3168ff34b1da9b0f311a8f1cfd694ec96b0603dff1c79438" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:0b929ac182f5ace000d459c59c2c9c33047e20e935f8e39371fa6e3b85d56f4a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:f09b286faeff3c750a879d336fb6d8713206fc97af3adc14def0cdd349df6000" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:1a7ed2d9d039bd41e889f6fb9364554052ca21ce823580f6a07c4ec245c1f5d6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:129a150f5c445165ff941fc02ee27df65940fcb8a22a61828b1853c98763a64b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:1fb5699e4464afe5c7e65fa51d4f99e0b2eadcc176e4aa33600a3df7801d6662" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:9a2bce789a5ea90e51a02dfcc39e31b7f1e662bc3317979aa7e5538e3a034f72" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp311-cp311-win32.whl", hash = "sha256:4afd5814270fdf6380616b321fd31435a462019d834f83c8611a0ce7484c7317" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp311-cp311-win_amd64.whl", hash = "sha256:acc130bc0375999da18e3d19e5a86403667ac0c4042a094fefb7eec8ebac7cf3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:d5e2439eecc762cd85e7bd37161d4714aa03a33c5ba884e26c81559817ca0925" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:3fc7cb4c1c744f8c05cd5f9438a3caa6ab94ce8344e952d7c45a8ed59dd88392" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:8fdbdb757d5390f7c675e558fd3186d590973244fab0c5fe63d373ade3e99d40" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:5bb1d0dbf99411f3d871deb6faa9aabb9d4e744d67dcaaa05399af89d847a91d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:d18a4865f46b8579d44e4fe1e2bcbc6472ad83d98e22a26c963d46e4c125ef0b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:bc570b5f14a79734437cb7b0500376b6b791153314986074486e0b0fa8d71d98" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:6d9187b01bebc3875bac9b087948a2bccefe464a7d8f627cf6e48b1bbae30f82" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:9e8659775f1adf02eb1e6f109751268e493c73716ca5761f8acb695e52a756ae" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:e8b2816ebef96d83657b56306152a93909a83f23994f4b30ad4573b00bd11bb9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp312-cp312-win32.whl", hash = "sha256:468090021f391fe0056ad3e807e3d9034e0fd01adcd3bdfba977b6fdf4213ea9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp312-cp312-win_amd64.whl", hash = "sha256:ec89ed91f2fa8e3f52ae53cd3cf640d6feff92ba90d62236a81e4e563ac0e991" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:6ed6ffac43aecfe6d86ec5b74b06a5be33d5bb9243d055141e8cabb12aa08125" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:35621ae4c00e056adb0009f8e86e28eb4a41a4bfa8f9bfa9fca7d343fe94f998" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:a604bf7a053f8362d27eb9fefd2097f82600b856d5abe996d623babd067b1ab5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:5cbabee4f083b6b4cd282f5b817a867cf0b1028c54d445b7ec7cfe6505057cf8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:49703ce2ddc220df165bd2962f8e03b84c89fee2d65e1c24a7defff6f988f4d6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:8112e52c5822fc4253f3901b676c55ddf288614dc7011634e2719718eaa187dc" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:9fee687dce376205d9a494e9c121e27183b2a3df18037f89d69bd7b35bcf59e2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:18983c537e04d11cf027fbb60a1e8dfd5190e2b60cc27bc0808e653e7b218d1b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:703919b1633412ab54bcf920ab388735832fdcb9f9a00ae49387f0fe67dad504" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313-win32.whl", hash = "sha256:abbb9e76177c35d4e8568e58650aa6926040d6a9f6f03435b7a522bf1c487f9a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313-win_amd64.whl", hash = "sha256:69606d7bb691b50a4240ce6b22ebb319c1cfb164e5f6569835058196e0f3a845" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313t-macosx_10_13_universal2.whl", hash = "sha256:4a721d3c943dae44f8e243b380cb645a709ba5bd35d3ad27bc2ed947e9c68192" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313t-macosx_10_13_x86_64.whl", hash = "sha256:766d8bbefcb9e00c3ac3b000d9acc51f1b399513f44d77dfe0eb026ad7c9a19b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313t-macosx_11_0_arm64.whl", hash = "sha256:e496a8ce2c256da1eb98bd15803a79bee00fc351f5dfb9ea82594a3f058309e0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:40d615e4fe22f4ad3528448c193b218e077656ca9ccb22ce2cb20db730f8d306" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313t-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:a5aaeff38654462bc4b09023918b7f21790efb807f54c000a39d41d69cf552cb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313t-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:9a7d15bbd2bc99e92e39f49a04653062ee6085c0e18b3b7512a4f2fe91f2d681" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313t-musllinux_1_2_aarch64.whl", hash = "sha256:e3890b508a23299083e065f435a492b5435eba6e304a7114d2f919d400888cc6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313t-musllinux_1_2_i686.whl", hash = "sha256:8c8b293cd65ad716d13d8dd3624e42e5a19cc2a2f1acc74b30c2c13f15cb61a6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313t-musllinux_1_2_x86_64.whl", hash = "sha256:4c82b8785d98cdd9fed4cac84d765d234ed3251bd6afe34cb7ac523cb93e8b4f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313t-win32.whl", hash = "sha256:13e6afb7fe71fe7485a4550a8844cc9ffbe263c0f1a1eea569bc7091d4898555" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-cp313-cp313t-win_amd64.whl", hash = "sha256:eaf675418ed6b3b31c7a989fd007fa7c3be66ce14e5c3b27336383604c9da85c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/wrapt/1.17.2/wrapt-1.17.2-py3-none-any.whl", hash = "sha256:b18f2d1533a71f069c7f82d524a52599053d4c7166e9dd374ae2136b7f40f7c8" },
]

[[package]]
name = "yarl"
version = "1.18.3"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "idna" },
    { name = "multidict" },
    { name = "propcache" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3.tar.gz", hash = "sha256:ac1801c45cbf77b6c99242eeff4fffb5e4e73a800b5c4ad4fc0be5def634d2e1" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-macosx_10_9_universal2.whl", hash = "sha256:8503ad47387b8ebd39cbbbdf0bf113e17330ffd339ba1144074da24c545f0069" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:02ddb6756f8f4517a2d5e99d8b2f272488e18dd0bfbc802f31c16c6c20f22193" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:67a283dd2882ac98cc6318384f565bffc751ab564605959df4752d42483ad889" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:d980e0325b6eddc81331d3f4551e2a333999fb176fd153e075c6d1c2530aa8a8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:b643562c12680b01e17239be267bc306bbc6aac1f34f6444d1bded0c5ce438ca" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:c017a3b6df3a1bd45b9fa49a0f54005e53fbcad16633870104b66fa1a30a29d8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:75674776d96d7b851b6498f17824ba17849d790a44d282929c42dbb77d4f17ae" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:ccaa3a4b521b780a7e771cc336a2dba389a0861592bbce09a476190bb0c8b4b3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:2d06d3005e668744e11ed80812e61efd77d70bb7f03e33c1598c301eea20efbb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-musllinux_1_2_armv7l.whl", hash = "sha256:9d41beda9dc97ca9ab0b9888cb71f7539124bc05df02c0cff6e5acc5a19dcc6e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:ba23302c0c61a9999784e73809427c9dbedd79f66a13d84ad1b1943802eaaf59" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-musllinux_1_2_ppc64le.whl", hash = "sha256:6748dbf9bfa5ba1afcc7556b71cda0d7ce5f24768043a02a58846e4a443d808d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-musllinux_1_2_s390x.whl", hash = "sha256:0b0cad37311123211dc91eadcb322ef4d4a66008d3e1bdc404808992260e1a0e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:0fb2171a4486bb075316ee754c6d8382ea6eb8b399d4ec62fde2b591f879778a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-win32.whl", hash = "sha256:61b1a825a13bef4a5f10b1885245377d3cd0bf87cba068e1d9a88c2ae36880e1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp311-cp311-win_amd64.whl", hash = "sha256:b9d60031cf568c627d028239693fd718025719c02c9f55df0a53e587aab951b5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:1dd4bdd05407ced96fed3d7f25dbbf88d2ffb045a0db60dbc247f5b3c5c25d50" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:7c33dd1931a95e5d9a772d0ac5e44cac8957eaf58e3c8da8c1414de7dd27c576" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:25b411eddcfd56a2f0cd6a384e9f4f7aa3efee14b188de13048c25b5e91f1640" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:436c4fc0a4d66b2badc6c5fc5ef4e47bb10e4fd9bf0c79524ac719a01f3607c2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:e35ef8683211db69ffe129a25d5634319a677570ab6b2eba4afa860f54eeaf75" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:84b2deecba4a3f1a398df819151eb72d29bfeb3b69abb145a00ddc8d30094512" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:00e5a1fea0fd4f5bfa7440a47eff01d9822a65b4488f7cff83155a0f31a2ecba" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:d0e883008013c0e4aef84dcfe2a0b172c4d23c2669412cf5b3371003941f72bb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:5a3f356548e34a70b0172d8890006c37be92995f62d95a07b4a42e90fba54272" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-musllinux_1_2_armv7l.whl", hash = "sha256:ccd17349166b1bee6e529b4add61727d3f55edb7babbe4069b5764c9587a8cc6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:b958ddd075ddba5b09bb0be8a6d9906d2ce933aee81100db289badbeb966f54e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:c7d79f7d9aabd6011004e33b22bc13056a3e3fb54794d138af57f5ee9d9032cb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:4891ed92157e5430874dad17b15eb1fda57627710756c27422200c52d8a4e393" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:ce1af883b94304f493698b00d0f006d56aea98aeb49d75ec7d98cd4a777e9285" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-win32.whl", hash = "sha256:f91c4803173928a25e1a55b943c81f55b8872f0018be83e3ad4938adffb77dd2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp312-cp312-win_amd64.whl", hash = "sha256:7e2ee16578af3b52ac2f334c3b1f92262f47e02cc6193c598502bd46f5cd1477" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:90adb47ad432332d4f0bc28f83a5963f426ce9a1a8809f5e584e704b82685dcb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:913829534200eb0f789d45349e55203a091f45c37a2674678744ae52fae23efa" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:ef9f7768395923c3039055c14334ba4d926f3baf7b776c923c93d80195624782" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:88a19f62ff30117e706ebc9090b8ecc79aeb77d0b1f5ec10d2d27a12bc9f66d0" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:e17c9361d46a4d5addf777c6dd5eab0715a7684c2f11b88c67ac37edfba6c482" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:1a74a13a4c857a84a845505fd2d68e54826a2cd01935a96efb1e9d86c728e186" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:41f7ce59d6ee7741af71d82020346af364949314ed3d87553763a2df1829cc58" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:f52a265001d830bc425f82ca9eabda94a64a4d753b07d623a9f2863fde532b53" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:82123d0c954dc58db301f5021a01854a85bf1f3bb7d12ae0c01afc414a882ca2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-musllinux_1_2_armv7l.whl", hash = "sha256:2ec9bbba33b2d00999af4631a3397d1fd78290c48e2a3e52d8dd72db3a067ac8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:fbd6748e8ab9b41171bb95c6142faf068f5ef1511935a0aa07025438dd9a9bc1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:877d209b6aebeb5b16c42cbb377f5f94d9e556626b1bfff66d7b0d115be88d0a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:b464c4ab4bfcb41e3bfd3f1c26600d038376c2de3297760dfe064d2cb7ea8e10" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:8d39d351e7faf01483cc7ff7c0213c412e38e5a340238826be7e0e4da450fdc8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-win32.whl", hash = "sha256:61ee62ead9b68b9123ec24bc866cbef297dd266175d53296e2db5e7f797f902d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-cp313-cp313-win_amd64.whl", hash = "sha256:578e281c393af575879990861823ef19d66e2b1d0098414855dd367e234f5b3c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/yarl/1.18.3/yarl-1.18.3-py3-none-any.whl", hash = "sha256:b57f4f58099328dfb26c6a771d09fb20dbbae81d20cfb66141251ea063bd101b" },
]
