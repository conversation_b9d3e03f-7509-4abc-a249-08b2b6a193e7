import pytest
import uuid
from unittest.mock import patch, AsyncMock, MagicMock

from gametime_protos.mlp.prism.v1.service_pb2 import (
    FamiliesServiceCreateRequest,
    FamiliesServiceDeleteRequest,
    FamiliesServiceDescribeRequest,
    FamiliesServiceListRequest,
    FamilyConfig as FamilyConfigProto,
    SourceConfig as SourceConfigProto,
    BatchSourceConfig as BatchSourceConfigProto,
    FeatureConfig as FeatureConfigProto,
    AggregationFunction as AggregationFunctionProto,
    FamiliesServiceUpsertFromYAMLRequest,
)
from gametime_protos.mlp.prism.v1.service_pb2_grpc import FamiliesServiceStub
from google.protobuf.wrappers_pb2 import StringValue
import grpc

from src.families import Registry, FamilyStatus, Family, FamilyNotFoundError
from src.shared import FamilyDetails, TASK_QUEUE_NAME
from src.workflows import FamilyPipeline
from temporalio.client import Client as TemporalClient


VALID_FAMILY_YAML_TEMPLATE = """
name: {family_name}
config:
  source:
    batch:
      table: "my_event_table"
      late_arriving_data_lag_seconds: 3600
    query: "SELECT id, ts, entity_id, feature_value FROM {{ ref }}"
  id_column: "id"
  timestamp_column: "ts"
  identifier_columns: ["entity_id"]
  features:
    - column: "feature_value"
      aggregations: ["SUM", "COUNT"]
    - column: "another_col"
      aggregations: ["AVG"]
"""

INVALID_YAML_SYNTAX = """
name: invalid_family
config:
  source
    batch: table: "bad_indent"
"""

YAML_MISSING_NAME = """
config:
  source:
    batch:
      table: "my_event_table"
"""

YAML_INVALID_AGGREGATION_TEMPLATE = """
name: {family_name}
config:
  source:
    batch:
      table: "my_event_table"
      late_arriving_data_lag_seconds: 3600
    query: "SELECT id, ts, entity_id, feature_value FROM {{ ref }}"
  id_column: "id"
  timestamp_column: "ts"
  identifier_columns: ["entity_id"]
  features:
    - column: "feature_value"
      aggregations: ["SUM", "INVALID_AGG"]
"""


@pytest.fixture
def test_family_name() -> str:
    """Generate a unique test family name."""
    return f"test-family-{uuid.uuid4().hex[:8]}"


@pytest.fixture
def test_family_config_proto() -> FamilyConfigProto:
    """Create a test family configuration proto."""
    batch_config = BatchSourceConfigProto(
        table="test_events", late_arriving_data_lag_seconds=3600
    )
    query_wrapper = StringValue(
        value="SELECT event_id, ts, user_id, value FROM {{ ref }}"
    )
    source_config = SourceConfigProto(batch=batch_config, query=query_wrapper)
    feature_config = FeatureConfigProto(
        column="value",
        aggregations=[AggregationFunctionProto.AGGREGATION_FUNCTION_COUNT],
    )
    return FamilyConfigProto(
        source=source_config,
        id_column="event_id",
        timestamp_column="ts",
        identifier_columns=["user_id"],
        features=[feature_config],
    )


@pytest.mark.asyncio
async def test_create_family_success(
    families_stub: FamiliesServiceStub,
    registry: Registry,
    test_family_name: str,
    test_family_config_proto: FamilyConfigProto,
    temporal: str,  # Ensure temporal fixture is used
    temporal_worker,  # Ensure worker is running
):
    """Test successful family creation."""
    request = FamiliesServiceCreateRequest(
        name=test_family_name, config=test_family_config_proto
    )

    mock_temporal_client = MagicMock(spec=TemporalClient)
    mock_temporal_client.start_workflow = AsyncMock(
        return_value=AsyncMock()
    )  # Mock handle
    mock_temporal_client.get_workflow_handle = MagicMock(
        side_effect=Exception("Workflow not found initially")
    )

    with patch("src.servicers.families.ClientWrapper") as MockClientWrapper:
        mock_wrapper_instance = MockClientWrapper.return_value
        mock_wrapper_instance.client = mock_temporal_client

        await families_stub.Create(request)

    family = await registry.fetch_one(test_family_name)
    assert family.name == test_family_name
    assert family.status == FamilyStatus.FAMILY_STATUS_INITIALIZING

    mock_temporal_client.start_workflow.assert_called_once()
    args, kwargs = mock_temporal_client.start_workflow.call_args
    assert args[0] == FamilyPipeline.run
    assert args[1].name == test_family_name
    assert kwargs["id"] == FamilyPipeline.id_for(FamilyDetails(name=test_family_name))
    assert kwargs["task_queue"] == TASK_QUEUE_NAME


@pytest.mark.asyncio
async def test_create_family_already_exists(
    families_stub: FamiliesServiceStub,
    test_family_name: str,
    test_family_config_proto: FamilyConfigProto,
    temporal_worker,
):
    """Test creating a family that already exists."""
    request = FamiliesServiceCreateRequest(
        name=test_family_name, config=test_family_config_proto
    )
    await families_stub.Create(request)

    with pytest.raises(grpc.aio.AioRpcError) as e:
        await families_stub.Create(request)
    assert e.value.code() == grpc.StatusCode.ALREADY_EXISTS


@pytest.mark.asyncio
async def test_upsert_from_yaml_create_new_family(
    families_stub: FamiliesServiceStub,
    registry: Registry,
    temporal: str,
    temporal_worker,
):
    """Test creating a new family using UpsertFromYAML."""
    family_name = f"yaml_new_fam_srv_{uuid.uuid4().hex[:6]}"
    yaml_content = VALID_FAMILY_YAML_TEMPLATE.format(family_name=family_name)
    request = FamiliesServiceUpsertFromYAMLRequest(yaml_content=yaml_content)

    with pytest.raises(FamilyNotFoundError):
        await registry.fetch_one(family_name)

    mock_temporal_client = MagicMock(spec=TemporalClient)
    mock_temporal_client.start_workflow = AsyncMock(return_value=AsyncMock())
    mock_temporal_client.get_workflow_handle = MagicMock(
        side_effect=Exception("Workflow not found initially")
    )
    mock_temporal_client.execute_activity = AsyncMock()

    with patch("src.servicers.families.ClientWrapper") as MockClientWrapper:
        mock_wrapper_instance = MockClientWrapper.return_value
        mock_wrapper_instance.client = mock_temporal_client

        response = await families_stub.UpsertFromYAML(request)

    assert response.family.name == family_name
    assert not response.family.draft
    assert response.family.status == FamilyStatus.FAMILY_STATUS_INITIALIZING.value

    fetched_family = await registry.fetch_one(family_name)
    assert fetched_family.name == family_name
    assert not fetched_family.draft
    assert fetched_family.status == FamilyStatus.FAMILY_STATUS_INITIALIZING
    assert fetched_family.config.source.batch.table == "my_event_table"
    assert len(fetched_family.config.features[0].aggregations) == 2

    mock_temporal_client.start_workflow.assert_called_once()


@pytest.mark.asyncio
async def test_upsert_from_yaml_update_existing_family(
    families_stub: FamiliesServiceStub,
    registry: Registry,
    temporal: str,
    temporal_worker,
):
    """Test updating an existing family (possibly draft) using UpsertFromYAML."""
    family_name = f"yaml_update_fam_srv_{uuid.uuid4().hex[:6]}"

    from src.families import (
        FamilyConfig as PyFamilyConfig,
        SourceConfig,
        BatchSourceConfig,
        FeatureConfig,
        AggregationFunction,
    )

    batch_cfg = BatchSourceConfig(
        table="old_table_srv", late_arriving_data_lag_seconds=1800
    )
    source_cfg = SourceConfig(batch=batch_cfg, query="SELECT * FROM old_table_srv")
    feature_cfg_list = [
        FeatureConfig(
            column="old_col_srv",
            aggregations=[AggregationFunction.AGGREGATION_FUNCTION_COUNT],
        )
    ]
    config_obj = PyFamilyConfig(
        source=source_cfg,
        id_column="id_srv",
        timestamp_column="ts_srv",
        identifier_columns=["e_id_srv"],
        features=feature_cfg_list,
    )

    initial_family_obj = Family(
        name=family_name,
        config=config_obj,
        draft=True,
        status=FamilyStatus.FAMILY_STATUS_RUNNING,
    )
    await registry.add(initial_family_obj)
    initial_family_id = (await registry.fetch_one(family_name)).id

    yaml_content = VALID_FAMILY_YAML_TEMPLATE.format(family_name=family_name)
    yaml_content = yaml_content.replace("my_event_table", "updated_event_table_srv")
    request = FamiliesServiceUpsertFromYAMLRequest(yaml_content=yaml_content)

    mock_temporal_client = MagicMock(spec=TemporalClient)
    mock_temporal_client.start_workflow = AsyncMock(return_value=AsyncMock())
    mock_workflow_handle = AsyncMock()
    mock_workflow_handle.terminate = AsyncMock()
    mock_temporal_client.get_workflow_handle = MagicMock(
        return_value=mock_workflow_handle
    )
    mock_temporal_client.execute_activity = AsyncMock()

    with patch("src.servicers.families.ClientWrapper") as MockClientWrapper:
        mock_wrapper_instance = MockClientWrapper.return_value
        mock_wrapper_instance.client = mock_temporal_client

        response = await families_stub.UpsertFromYAML(request)

    assert response.family.name == family_name
    assert not response.family.draft
    assert response.family.status == FamilyStatus.FAMILY_STATUS_INITIALIZING.value
    assert response.family.config.source.batch.table == "updated_event_table_srv"

    fetched_family = await registry.fetch_one(family_name)
    assert fetched_family.id == initial_family_id
    assert not fetched_family.draft
    assert fetched_family.status == FamilyStatus.FAMILY_STATUS_INITIALIZING
    assert fetched_family.config.source.batch.table == "updated_event_table_srv"

    mock_temporal_client.get_workflow_handle.assert_called_once()
    mock_workflow_handle.terminate.assert_called_once()
    mock_temporal_client.execute_activity.assert_called_once()
    mock_temporal_client.start_workflow.assert_called_once()


@pytest.mark.asyncio
async def test_upsert_from_yaml_invalid_syntax(families_stub: FamiliesServiceStub):
    """Test UpsertFromYAML with invalid YAML syntax."""
    request = FamiliesServiceUpsertFromYAMLRequest(yaml_content=INVALID_YAML_SYNTAX)
    with pytest.raises(grpc.aio.AioRpcError) as e:
        await families_stub.UpsertFromYAML(request)
    assert e.value.code() == grpc.StatusCode.INVALID_ARGUMENT
    assert "Invalid YAML" in e.value.details()


@pytest.mark.asyncio
async def test_upsert_from_yaml_missing_name_in_yaml(
    families_stub: FamiliesServiceStub,
):
    """Test UpsertFromYAML when the 'name' field is missing in the YAML content."""
    request = FamiliesServiceUpsertFromYAMLRequest(yaml_content=YAML_MISSING_NAME)
    with pytest.raises(grpc.aio.AioRpcError) as e:
        await families_stub.UpsertFromYAML(request)
    assert e.value.code() == grpc.StatusCode.INVALID_ARGUMENT
    assert "Family 'name' missing in YAML" in e.value.details()


@pytest.mark.asyncio
async def test_upsert_from_yaml_invalid_aggregation_string(
    families_stub: FamiliesServiceStub,
):
    """Test UpsertFromYAML with an invalid aggregation function string."""
    family_name = f"yaml_invalid_agg_srv_{uuid.uuid4().hex[:6]}"
    yaml_content = YAML_INVALID_AGGREGATION_TEMPLATE.format(family_name=family_name)
    request = FamiliesServiceUpsertFromYAMLRequest(yaml_content=yaml_content)
    with pytest.raises(grpc.aio.AioRpcError) as e:
        await families_stub.UpsertFromYAML(request)
    assert e.value.code() == grpc.StatusCode.INVALID_ARGUMENT
    assert "Invalid aggregation function string: INVALID_AGG" in e.value.details()


@pytest.mark.asyncio
async def test_delete_family_success(
    families_stub: FamiliesServiceStub,
    registry: Registry,
    test_family_name: str,
    test_family_config_proto: FamilyConfigProto,
    temporal: str,
    temporal_worker,
):
    """Test successful family deletion."""
    create_request = FamiliesServiceCreateRequest(
        name=test_family_name, config=test_family_config_proto
    )
    await families_stub.Create(create_request)
    await registry.fetch_one(test_family_name)  # Ensure it's there

    delete_request = FamiliesServiceDeleteRequest(name=test_family_name)

    mock_temporal_client = MagicMock(spec=TemporalClient)
    mock_workflow_handle = AsyncMock()
    mock_workflow_handle.terminate = AsyncMock()
    mock_temporal_client.get_workflow_handle = MagicMock(
        return_value=mock_workflow_handle
    )
    mock_temporal_client.execute_activity = AsyncMock()  # For delete_schedule

    with patch("src.servicers.families.ClientWrapper") as MockClientWrapper:
        mock_wrapper_instance = MockClientWrapper.return_value
        mock_wrapper_instance.client = mock_temporal_client
        await families_stub.Delete(delete_request)

    with pytest.raises(FamilyNotFoundError):
        await registry.fetch_one(test_family_name)

    mock_temporal_client.execute_activity.assert_called_once()
    mock_temporal_client.get_workflow_handle.assert_called_once()
    mock_workflow_handle.terminate.assert_called_once()


@pytest.mark.asyncio
async def test_delete_family_not_found(
    families_stub: FamiliesServiceStub, test_family_name: str, temporal_worker
):
    """Test deleting a family that does not exist."""
    delete_request = FamiliesServiceDeleteRequest(name=test_family_name)
    with pytest.raises(grpc.aio.AioRpcError) as e:
        await families_stub.Delete(delete_request)
    assert e.value.code() == grpc.StatusCode.NOT_FOUND


@pytest.mark.asyncio
async def test_describe_family_success(
    families_stub: FamiliesServiceStub,
    registry: Registry,
    test_family_name: str,
    test_family_config_proto: FamilyConfigProto,
    temporal_worker,
):
    """Test describing an existing family."""
    create_request = FamiliesServiceCreateRequest(
        name=test_family_name, config=test_family_config_proto
    )
    await families_stub.Create(create_request)

    describe_request = FamiliesServiceDescribeRequest(name=test_family_name)
    response = await families_stub.Describe(describe_request)
    assert response.family.name == test_family_name
    assert response.family.config.id_column == test_family_config_proto.id_column


@pytest.mark.asyncio
async def test_describe_family_not_found(
    families_stub: FamiliesServiceStub, test_family_name: str, temporal_worker
):
    """Test describing a family that does not exist."""
    describe_request = FamiliesServiceDescribeRequest(name=test_family_name)
    with pytest.raises(grpc.aio.AioRpcError) as e:
        await families_stub.Describe(describe_request)
    assert e.value.code() == grpc.StatusCode.NOT_FOUND


@pytest.mark.asyncio
async def test_list_families(
    families_stub: FamiliesServiceStub,
    registry: Registry,
    test_family_config_proto: FamilyConfigProto,
    temporal_worker,
):
    """Test listing families."""
    family_name_1 = f"list-fam-1-{uuid.uuid4().hex[:4]}"
    family_name_2 = f"list-fam-2-{uuid.uuid4().hex[:4]}"

    await families_stub.Create(
        FamiliesServiceCreateRequest(
            name=family_name_1, config=test_family_config_proto
        )
    )
    await families_stub.Create(
        FamiliesServiceCreateRequest(
            name=family_name_2, config=test_family_config_proto, draft=True
        )
    )

    response_all = await families_stub.List(FamiliesServiceListRequest())
    assert len(response_all.families) >= 2
    assert any(f.name == family_name_1 for f in response_all.families)
    assert any(f.name == family_name_2 for f in response_all.families)

    response_no_draft = await families_stub.List(
        FamiliesServiceListRequest(exclude_draft=True)
    )
    assert len(response_no_draft.families) >= 1
    assert any(f.name == family_name_1 for f in response_no_draft.families)
    assert not any(f.name == family_name_2 for f in response_no_draft.families)
